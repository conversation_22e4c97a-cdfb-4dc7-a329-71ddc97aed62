{"cells": [{"attachments": {"d98ed25c-51cb-441f-a6f4-016921d59fc3.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "a3e3ebc4-57af-4fe4-bdd3-36aff67bf276", "metadata": {}, "source": ["# Hierarchical Agent Teams\n", "\n", "In our previous example ([Agent Supervisor](../agent_supervisor)), we introduced the concept of a single [supervisor node](https://langchain-ai.github.io/langgraph/concepts/multi_agent/#supervisor) to route work between different worker nodes.\n", "\n", "But what if the job for a single worker becomes too complex? What if the number of workers becomes too large?\n", "\n", "For some applications, the system may be more effective if work is distributed _hierarchically_.\n", "\n", "You can do this by composing different subgraphs and creating a top-level supervisor, along with mid-level supervisors.\n", "\n", "To do this, let's build a simple research assistant! The graph will look something like the following:\n", "\n", "![diagram](attachment:d98ed25c-51cb-441f-a6f4-016921d59fc3.png)\n", "\n", "This notebook is inspired by the paper [AutoGen: Enabling Next-Gen LLM Applications via Multi-Agent Conversation](https://arxiv.org/abs/2308.08155), by <PERSON>, et. al. In the rest of this notebook, you will:\n", "\n", "1. Define the agents' tools to access the web and write files\n", "2. Define some utilities to help create the graph and agents\n", "3. Create and define each team (web research + doc writing)\n", "4. Compose everything together.\n", "\n", "## Setup\n", "\n", "First, let's install our required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 1, "id": "0d30b6f7-3bec-4d9f-af50-43dfdc81ae6c", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:42.364369Z", "start_time": "2024-05-15T08:19:42.359273Z"}}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain_community langchain_anthropic langchain-tavily langchain_experimental"]}, {"cell_type": "code", "execution_count": null, "id": "30c2f3de-c730-4aec-85a6-af2c2f058803", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:42.395571Z", "start_time": "2024-05-15T08:19:42.365662Z"}}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_if_undefined(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"Please provide your {var}\")\n", "\n", "\n", "_set_if_undefined(\"OPENAI_API_KEY\")\n", "_set_if_undefined(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "04fdd0a3", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "354568e2-aef0-4af9-8a79-e64d3eea752f", "metadata": {}, "source": ["## Create Tools\n", "\n", "Each team will be composed of one or more agents each with one or more tools. Below, define all the tools to be used by your different teams.\n", "\n", "We'll start with the research team.\n", "\n", "**ResearchTeam tools**\n", "\n", "The research team can use a search engine and url scraper to find information on the web. Feel free to add additional functionality below to boost the team performance!"]}, {"cell_type": "code", "execution_count": null, "id": "4024eb89-843d-4cc3-ab3f-e1eb4d031179", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:44.477064Z", "start_time": "2024-05-15T08:19:42.397083Z"}}, "outputs": [], "source": ["from typing import Annotated, List\n", "\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_tavily import TavilySearch\n", "from langchain_core.tools import tool\n", "\n", "tavily_tool = TavilySearch(max_results=5)\n", "\n", "\n", "@tool\n", "def scrape_webpages(urls: List[str]) -> str:\n", "    \"\"\"Use requests and bs4 to scrape the provided web pages for detailed information.\"\"\"\n", "    loader = WebBaseLoader(urls)\n", "    docs = loader.load()\n", "    return \"\\n\\n\".join(\n", "        [\n", "            f'<Document name=\"{doc.metadata.get(\"title\", \"\")}\">\\n{doc.page_content}\\n</Document>'\n", "            for doc in docs\n", "        ]\n", "    )"]}, {"cell_type": "markdown", "id": "1c427982-fadf-4721-a77e-2465df9fc6bc", "metadata": {}, "source": ["**Document writing team tools**\n", "\n", "Next up, we will give some tools for the doc writing team to use.\n", "We define some bare-bones file-access tools below.\n", "\n", "Note that this gives the agents access to your file-system, which can be unsafe. We also haven't optimized the tool descriptions for performance."]}, {"cell_type": "code", "execution_count": 4, "id": "f20a18ca-2709-4c12-84f3-88678591a9fa", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:44.538421Z", "start_time": "2024-05-15T08:19:44.479132Z"}}, "outputs": [], "source": ["from pathlib import Path\n", "from tempfile import TemporaryDirectory\n", "from typing import Dict, Optional\n", "\n", "from langchain_experimental.utilities import PythonREPL\n", "from typing_extensions import TypedDict\n", "\n", "_TEMP_DIRECTORY = TemporaryDirectory()\n", "WORKING_DIRECTORY = Path(_TEMP_DIRECTORY.name)\n", "\n", "\n", "@tool\n", "def create_outline(\n", "    points: Annotated[List[str], \"List of main points or sections.\"],\n", "    file_name: Annotated[str, \"File path to save the outline.\"],\n", ") -> Annotated[str, \"Path of the saved outline file.\"]:\n", "    \"\"\"Create and save an outline.\"\"\"\n", "    with (WORKING_DIRECTORY / file_name).open(\"w\") as file:\n", "        for i, point in enumerate(points):\n", "            file.write(f\"{i + 1}. {point}\\n\")\n", "    return f\"Outline saved to {file_name}\"\n", "\n", "\n", "@tool\n", "def read_document(\n", "    file_name: Annotated[str, \"File path to read the document from.\"],\n", "    start: Annotated[Optional[int], \"The start line. Default is 0\"] = None,\n", "    end: Annotated[Optional[int], \"The end line. Default is None\"] = None,\n", ") -> str:\n", "    \"\"\"Read the specified document.\"\"\"\n", "    with (WORKING_DIRECTORY / file_name).open(\"r\") as file:\n", "        lines = file.readlines()\n", "    if start is None:\n", "        start = 0\n", "    return \"\\n\".join(lines[start:end])\n", "\n", "\n", "@tool\n", "def write_document(\n", "    content: Annotated[str, \"Text content to be written into the document.\"],\n", "    file_name: Annotated[str, \"File path to save the document.\"],\n", ") -> Annotated[str, \"Path of the saved document file.\"]:\n", "    \"\"\"Create and save a text document.\"\"\"\n", "    with (WORKING_DIRECTORY / file_name).open(\"w\") as file:\n", "        file.write(content)\n", "    return f\"Document saved to {file_name}\"\n", "\n", "\n", "@tool\n", "def edit_document(\n", "    file_name: Annotated[str, \"Path of the document to be edited.\"],\n", "    inserts: Annotated[\n", "        Dict[int, str],\n", "        \"Dictionary where key is the line number (1-indexed) and value is the text to be inserted at that line.\",\n", "    ],\n", ") -> Annotated[str, \"Path of the edited document file.\"]:\n", "    \"\"\"Edit a document by inserting text at specific line numbers.\"\"\"\n", "\n", "    with (WORKING_DIRECTORY / file_name).open(\"r\") as file:\n", "        lines = file.readlines()\n", "\n", "    sorted_inserts = sorted(inserts.items())\n", "\n", "    for line_number, text in sorted_inserts:\n", "        if 1 <= line_number <= len(lines) + 1:\n", "            lines.insert(line_number - 1, text + \"\\n\")\n", "        else:\n", "            return f\"Error: Line number {line_number} is out of range.\"\n", "\n", "    with (WORKING_DIRECTORY / file_name).open(\"w\") as file:\n", "        file.writelines(lines)\n", "\n", "    return f\"Document edited and saved to {file_name}\"\n", "\n", "\n", "# Warning: This executes code locally, which can be unsafe when not sandboxed\n", "\n", "repl = PythonREPL()\n", "\n", "\n", "@tool\n", "def python_repl_tool(\n", "    code: Annotated[str, \"The python code to execute to generate your chart.\"],\n", "):\n", "    \"\"\"Use this to execute python code. If you want to see the output of a value,\n", "    you should print it out with `print(...)`. This is visible to the user.\"\"\"\n", "    try:\n", "        result = repl.run(code)\n", "    except BaseException as e:\n", "        return f\"Failed to execute. Error: {repr(e)}\"\n", "    return f\"Successfully executed:\\n```python\\n{code}\\n```\\nStdout: {result}\""]}, {"cell_type": "markdown", "id": "504ee1c6-2b6a-439d-9046-df54e1e15698", "metadata": {}, "source": ["## Helper Utilities\n", "\n", "We are going to create a few utility functions to make it more concise when we want to:\n", "\n", "1. Create a worker agent.\n", "2. Create a supervisor for the sub-graph.\n", "\n", "These will simplify the graph compositional code at the end for us so it's easier to see what's going on."]}, {"cell_type": "code", "execution_count": 5, "id": "e09fb60f-1aac-455b-b67d-8d2e4ccfd747", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:46.559082Z", "start_time": "2024-05-15T08:19:44.541330Z"}}, "outputs": [], "source": ["from typing import List, Optional, Literal\n", "from langchain_core.language_models.chat_models import BaseChatModel\n", "\n", "from langgraph.graph import StateGraph, MessagesState, START, END\n", "from langgraph.types import Command\n", "from langchain_core.messages import HumanMessage, trim_messages\n", "\n", "\n", "class State(MessagesState):\n", "    next: str\n", "\n", "\n", "def make_supervisor_node(llm: BaseChatModel, members: list[str]) -> str:\n", "    options = [\"FINISH\"] + members\n", "    system_prompt = (\n", "        \"You are a supervisor tasked with managing a conversation between the\"\n", "        f\" following workers: {members}. Given the following user request,\"\n", "        \" respond with the worker to act next. Each worker will perform a\"\n", "        \" task and respond with their results and status. When finished,\"\n", "        \" respond with FINISH.\"\n", "    )\n", "\n", "    class Router(TypedDict):\n", "        \"\"\"Worker to route to next. If no workers needed, route to FINISH.\"\"\"\n", "\n", "        next: Literal[*options]\n", "\n", "    def supervisor_node(state: State) -> Command[Literal[*members, \"__end__\"]]:\n", "        \"\"\"An LLM-based router.\"\"\"\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "        ] + state[\"messages\"]\n", "        response = llm.with_structured_output(Router).invoke(messages)\n", "        goto = response[\"next\"]\n", "        if goto == \"FINISH\":\n", "            goto = END\n", "\n", "        return Command(goto=goto, update={\"next\": goto})\n", "\n", "    return supervisor_node"]}, {"cell_type": "markdown", "id": "00282b1f-bb4d-4ee7-9bae-e8e6f586f12e", "metadata": {}, "source": ["## Define Agent Teams\n", "\n", "Now we can get to define our hierarchical teams. \"Choose your player!\"\n", "\n", "### Research Team\n", "\n", "The research team will have a search agent and a web scraping \"research_agent\" as the two worker nodes. Let's create those, as well as the team supervisor."]}, {"cell_type": "code", "execution_count": 6, "id": "53db0c78-e357-48ba-ae5f-3fc04735a3b7", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:48.810290Z", "start_time": "2024-05-15T08:19:46.561088Z"}}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage\n", "from langchain_openai import ChatOpenAI\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "search_agent = create_react_agent(llm, tools=[tavily_tool])\n", "\n", "\n", "def search_node(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    result = search_agent.invoke(state)\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(content=result[\"messages\"][-1].content, name=\"search\")\n", "            ]\n", "        },\n", "        # We want our workers to ALWAYS \"report back\" to the supervisor when done\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "web_scraper_agent = create_react_agent(llm, tools=[scrape_webpages])\n", "\n", "\n", "def web_scraper_node(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    result = web_scraper_agent.invoke(state)\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(content=result[\"messages\"][-1].content, name=\"web_scraper\")\n", "            ]\n", "        },\n", "        # We want our workers to ALWAYS \"report back\" to the supervisor when done\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "research_supervisor_node = make_supervisor_node(llm, [\"search\", \"web_scraper\"])"]}, {"cell_type": "markdown", "id": "b01c6ee8-a461-4081-8a97-a3a06ec0f994", "metadata": {}, "source": ["Now that we've created the necessary components, defining their interactions is easy. Add the nodes to the team graph, and define the edges, which determine the transition criteria."]}, {"cell_type": "code", "execution_count": 7, "id": "1a7a1260-d9f6-4011-b2b1-13fab5126997", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:48.825649Z", "start_time": "2024-05-15T08:19:48.811753Z"}}, "outputs": [], "source": ["research_builder = StateGraph(State)\n", "research_builder.add_node(\"supervisor\", research_supervisor_node)\n", "research_builder.add_node(\"search\", search_node)\n", "research_builder.add_node(\"web_scraper\", web_scraper_node)\n", "\n", "research_builder.add_edge(START, \"supervisor\")\n", "research_graph = research_builder.compile()"]}, {"cell_type": "code", "execution_count": 8, "id": "110f59bed6134685", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:51.936523Z", "start_time": "2024-05-15T08:19:48.827798Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(research_graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "63ee8f2c-fbde-427b-ba54-ae0c7ce5fbfb", "metadata": {}, "source": ["We can give this team work directly. Try it out below."]}, {"cell_type": "code", "execution_count": 9, "id": "912b0604-a178-4246-a36f-2dedae606680", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:51.952470Z", "start_time": "2024-05-15T08:19:51.937879Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'supervisor': {'next': 'search'}}\n", "---\n", "{'search': {'messages': [HumanMessage(content=\"<PERSON>'s next tour is The Eras Tour, which includes both U.S. and international dates. She announced additional U.S. dates for 2024. You can find more details about the tour and ticket information on platforms like Ticketmaster and official announcements.\", additional_kwargs={}, response_metadata={}, name='search', id='4df8687b-50a8-4342-aad5-680732c4a10f')]}}\n", "---\n", "{'supervisor': {'next': 'web_scraper'}}\n", "---\n", "{'web_scraper': {'messages': [HumanMessage(content='<PERSON>\\'s next tour is \"The Eras Tour.\" Here are some of the upcoming international dates for 2024 that were listed on Ticketmaster:\\n\\n1. **Toronto, ON, Canada** at Rogers Centre\\n   - November 21, 2024\\n   - November 22, 2024\\n   - November 23, 2024\\n\\n2. **Vancouver, BC, Canada** at BC Place\\n   - December 6, 2024\\n   - December 7, 2024\\n   - December 8, 2024\\n\\nFor the most current information and additional dates, you can check platforms like Ticketmaster or <PERSON> Swift\\'s [official website](https://www.taylorswift.com/events).', additional_kwargs={}, response_metadata={}, name='web_scraper', id='27524ebc-d179-4733-831d-ee10a58a2528')]}}\n", "---\n", "{'supervisor': {'next': '__end__'}}\n", "---\n"]}], "source": ["for s in research_graph.stream(\n", "    {\"messages\": [(\"user\", \"when is <PERSON>'s next tour?\")]},\n", "    {\"recursion_limit\": 100},\n", "):\n", "    print(s)\n", "    print(\"---\")"]}, {"cell_type": "markdown", "id": "749b99ab-f6f0-4c5d-a90b-10102465d186", "metadata": {}, "source": ["### Document Writing Team\n", "\n", "Create the document writing team below using a similar approach. This time, we will give each agent access to different file-writing tools.\n", "\n", "Note that we are giving file-system access to our agent here, which is not safe in all cases."]}, {"cell_type": "code", "execution_count": 10, "id": "1bcdbf44-9481-430c-8429-fa142ed8a626", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:53.677722Z", "start_time": "2024-05-15T08:19:51.953933Z"}}, "outputs": [], "source": ["llm = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "doc_writer_agent = create_react_agent(\n", "    llm,\n", "    tools=[write_document, edit_document, read_document],\n", "    prompt=(\n", "        \"You can read, write and edit documents based on note-taker's outlines. \"\n", "        \"Don't ask follow-up questions.\"\n", "    ),\n", ")\n", "\n", "\n", "def doc_writing_node(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    result = doc_writer_agent.invoke(state)\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(content=result[\"messages\"][-1].content, name=\"doc_writer\")\n", "            ]\n", "        },\n", "        # We want our workers to ALWAYS \"report back\" to the supervisor when done\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "note_taking_agent = create_react_agent(\n", "    llm,\n", "    tools=[create_outline, read_document],\n", "    prompt=(\n", "        \"You can read documents and create outlines for the document writer. \"\n", "        \"Don't ask follow-up questions.\"\n", "    ),\n", ")\n", "\n", "\n", "def note_taking_node(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    result = note_taking_agent.invoke(state)\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(content=result[\"messages\"][-1].content, name=\"note_taker\")\n", "            ]\n", "        },\n", "        # We want our workers to ALWAYS \"report back\" to the supervisor when done\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "chart_generating_agent = create_react_agent(\n", "    llm, tools=[read_document, python_repl_tool]\n", ")\n", "\n", "\n", "def chart_generating_node(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    result = chart_generating_agent.invoke(state)\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(\n", "                    content=result[\"messages\"][-1].content, name=\"chart_generator\"\n", "                )\n", "            ]\n", "        },\n", "        # We want our workers to ALWAYS \"report back\" to the supervisor when done\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "doc_writing_supervisor_node = make_supervisor_node(\n", "    llm, [\"doc_writer\", \"note_taker\", \"chart_generator\"]\n", ")"]}, {"cell_type": "markdown", "id": "aee2cd9b-29aa-458e-903d-4e49179e5d59", "metadata": {}, "source": ["With the objects themselves created, we can form the graph."]}, {"cell_type": "code", "execution_count": 11, "id": "9c5c644f-8966-4d2e-98d2-80d73520e9fe", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:53.693123Z", "start_time": "2024-05-15T08:19:53.678906Z"}}, "outputs": [], "source": ["# Create the graph here\n", "paper_writing_builder = StateGraph(State)\n", "paper_writing_builder.add_node(\"supervisor\", doc_writing_supervisor_node)\n", "paper_writing_builder.add_node(\"doc_writer\", doc_writing_node)\n", "paper_writing_builder.add_node(\"note_taker\", note_taking_node)\n", "paper_writing_builder.add_node(\"chart_generator\", chart_generating_node)\n", "\n", "paper_writing_builder.add_edge(START, \"supervisor\")\n", "paper_writing_graph = paper_writing_builder.compile()"]}, {"cell_type": "code", "execution_count": 12, "id": "58e7d1e48a9c39a5", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:32:13.913188Z", "start_time": "2024-05-15T08:32:11.598993Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(paper_writing_graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 14, "id": "9860fd46-c24d-40a5-a6ba-e8fddcd43369", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:53.723467Z", "start_time": "2024-05-15T08:19:53.709307Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'supervisor': {'next': 'note_taker'}}\n", "---\n", "{'note_taker': {'messages': [HumanMessage(content='The outline for the poem about cats has been created and saved as \"cats_poem_outline.txt\".', additional_kwargs={}, response_metadata={}, name='note_taker', id='14a5d8ca-9092-416f-96ee-ba16686e8658')]}}\n", "---\n", "{'supervisor': {'next': 'doc_writer'}}\n", "---\n", "{'doc_writer': {'messages': [HumanMessage(content='The poem about cats has been written and saved as \"cats_poem.txt\".', additional_kwargs={}, response_metadata={}, name='doc_writer', id='c4e31a94-63ae-4632-9e80-1166f3f138b2')]}}\n", "---\n", "{'supervisor': {'next': '__end__'}}\n", "---\n"]}], "source": ["for s in paper_writing_graph.stream(\n", "    {\n", "        \"messages\": [\n", "            (\n", "                \"user\",\n", "                \"Write an outline for poem about cats and then write the poem to disk.\",\n", "            )\n", "        ]\n", "    },\n", "    {\"recursion_limit\": 100},\n", "):\n", "    print(s)\n", "    print(\"---\")"]}, {"cell_type": "markdown", "id": "f4b5b08d-9a9a-474a-94b4-f7aaa8ff19e6", "metadata": {}, "source": ["## Add Layers\n", "\n", "In this design, we are enforcing a top-down planning policy. We've created two graphs already, but we have to decide how to route work between the two.\n", "\n", "We'll create a _third_ graph to orchestrate the previous two, and add some connectors to define how this top-level state is shared between the different graphs."]}, {"cell_type": "code", "execution_count": 15, "id": "8cbfbe34-43f5-4a3d-8e9b-6a1d9b339aec", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import BaseMessage\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "teams_supervisor_node = make_supervisor_node(llm, [\"research_team\", \"writing_team\"])"]}, {"cell_type": "code", "execution_count": 16, "id": "4880e573-612f-4d24-97c1-2079382a4a2f", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:55.469348Z", "start_time": "2024-05-15T08:19:55.455831Z"}}, "outputs": [], "source": ["def call_research_team(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    response = research_graph.invoke({\"messages\": state[\"messages\"][-1]})\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(\n", "                    content=response[\"messages\"][-1].content, name=\"research_team\"\n", "                )\n", "            ]\n", "        },\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "def call_paper_writing_team(state: State) -> Command[Literal[\"supervisor\"]]:\n", "    response = paper_writing_graph.invoke({\"messages\": state[\"messages\"][-1]})\n", "    return Command(\n", "        update={\n", "            \"messages\": [\n", "                HumanMessage(\n", "                    content=response[\"messages\"][-1].content, name=\"writing_team\"\n", "                )\n", "            ]\n", "        },\n", "        goto=\"supervisor\",\n", "    )\n", "\n", "\n", "# Define the graph.\n", "super_builder = StateGraph(State)\n", "super_builder.add_node(\"supervisor\", teams_supervisor_node)\n", "super_builder.add_node(\"research_team\", call_research_team)\n", "super_builder.add_node(\"writing_team\", call_paper_writing_team)\n", "\n", "super_builder.add_edge(START, \"supervisor\")\n", "super_graph = super_builder.compile()"]}, {"cell_type": "code", "execution_count": 17, "id": "270ff3ae26cd42ff", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:32:33.694459Z", "start_time": "2024-05-15T08:32:31.524790Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAc4AAAD5CAIAAAAhjEJGAAAAAXNSR0IArs4c6QAAIABJREFUeJzt3XlcTPv/B/DPLM1MM8207ytFSijFJVG0kLJUxL1xrReXLJfsO1e2y7W7l4SruGTLrpIlWVIKkSgVad9rpplm+/1x/PrWNYXMdGam9/PhD53OnPOuZl7zmc/5nM+HIBaLEQAAAFki4l0AAAAoP4haAACQOYhaAACQOYhaAACQOYhaAACQOYhaAACQOTLeBYBvVpRTz64VcmqFIoGYxxXhXc5XodCIVFUinUliqJN1jKh4lwNAeyPAuFqFIBaLXyfVvktn56SzzW3oJDKBziRp6FEa6hUjaokkVFXK59QKaXRiwTtuJztG5x4M0650vOsCoJ1A1CqA1DuVT29VmtsyOtsxOtkxCAQC3hV9l9pKfk46u/Qjr7ywYcAIHWMrVbwrAkDmIGrlWv5bzo3jRd2cWM4jtIkkxU7YzxXncR9cLlfXIQ8Zr493LQDIFkSt/HqeUJWTzvaaaKCqRsK7FhnKz+JcDSv8cYkZS0sF71oAkBWIWjmVkVRT8oHnGqCLdyHtoYErOrXtfeBCU+V+UwEdGUStPEq8VNbAEw0eq4d3Ie3qxO+5w6cZahvC+ASghGBcrdzJTK5l1wg6Ws4ihCasND+17QPeVQAgExC18qU0n5uXwfaaYIB3ITggEAg/LTW7cbwQ70IAkD6IWvlyP7rcth8L7ypwo2VAIasQM5Jq8C4EACmDqJUj719ziCRk0qVDD+x3HqH94HI53lUAIGUQtXLk9ZOaASN18K4CZ3QmuZer+suH1XgXAoA0QdTKi9pKfsE7brvND1BXV/f69es2P7ywsLCgoECqFf2PYSfVzORaGR0cAFxA1MqLnHR2JztGu51u/Pjx0dHRbXtsfn7+yJEjX716Je2iPjG2VC0raODVC2V0fADaH0StvCjK43axV2u30zU0NLTtgWKxWCAQyHo4ts0PzLwMjkxPAUB7gqiVFwXZXKaWTOa0PHbs2PDhw11cXKZNm5aUlIQQ8vX1raioiIqKcnJy8vX1xZJ3//79I0eO/OGHH3x8fA4cOCAUfmpUbt261cvL6969e35+fk5OTtevXx8zZgxCaNmyZU5OTuvWrZNFzTQ6qaKojW8GAMghmK9WXrBrBAyW9P8cSUlJ+/btGzZsmLOz84MHDzgcDkJo27ZtwcHBjo6OQUFBFAoFIUQikR4/fjxo0CATE5PMzMzw8HAWizVhwgTsIHV1dQcOHFi2bFl9fX3//v2JROKqVatmzZrl5OSkpaUl9ZoRQnQWqTiXJ4sjA4ALiFq5UF8npKoSZTF3F3bxKjAwsGfPnsOHD8c22trakslkHR0de3t7bAuJRDp+/Hjj9Iz5+fnx8fGNUdvQ0LBq1So7Ozvsy27duiGELCwsGh8udQwWmV3DltHBAWh/ELVyQSgQqTJlMtOKi4sLi8VavXr14sWLXVxcWtmzoqLi8OHDjx49qqmpQQgxmczGb9FotMacbR8kMoFEVrZJI0FHBn21ckFNQ6WyiC+LI+vo6ISHh5ubmy9YsGDatGklJSUSdysvLw8KCkpKSvr111/37t1rY2PT2FeLEKLT2/uuiroqAYUGT06gPODZLC/oTBK7RiCLI1tYWOzZs+fgwYNZWVlNr2I1HUVw7ty5ioqKAwcODB06tHv37gYGOE/CwKkR0lkwoSJQHhC18sK0q6qMohYb19WnT5+BAwc23ragqqpaVlbWuE9VVZWmpmZjwlZVVbUynItGoyGESktLZVEtRigQaepRZHd8ANoZ9NXKC019SvYztp4JTbqHffny5dKlSwMDA+l0+oMHD2xtbbHtDg4ON27cOHbsGIvF6tmzp5OT05kzZw4ePNirV6/4+PjExESRSFRVVaWhofH5MfX19Y2NjSMiIlRVVaurq8ePH0+lSvkmt5ePasYuMJXuMQHAEbRq5UUnO0ZOuvSvuVMolE6dOh09enTfvn0ODg6rV6/Gts+bN8/JySksLOzo0aMfPnwYMmTI9OnTo6KiVq5cyefzjx07ZmFhcfr0aYnHJBAIoaGhDAbjjz/+uHz5ckVFhXRrLv3IU2WQ1DSgHQCUB6zCIEeuhBUM8teFJbae36sSCsUOgzXxLgQAqYGGgxyxsld7fK3Cc0KLy8euWLHiwYMHn2/X19cvLi7+fLu6unqbJzr4evfv31+1atXn28VisVgsJhIlfHK6cuWKmlqLdyHfu1AW/KeVtMsEAE/QqpUvkZvzvKcaaulLviJUUVHB5XI/387n81VUJLSFiURiO4wl4HK5EvsQRCKRSCQikyW8nRsYGEiMYITQg8tlVDrJ0R2atECpQNTKl9xX7PevOYP8O8RCuZ9rqBdeP140apYx3oUAIGVwWUy+WNgyqKrEpJtSvtCkKP7944Nbx1u/EnQEELVy5wdv7bKPvBf3O9wyBBcPfHQZraOu3dGvCgKlBB0IcirhYqm6jkpPFwnDWpVS9MGP/X219UylPKwYADkBrVo5NXC0bvnHhoQLMrwjS05wagXH1uf2HKQBOQuUGLRq5dqL+9VJNyucfbVtflDCFcv5DaIHl8urS/mDx+kyNaHfACgziFp5x6kVPLhSXl7Q0NVRrbOdmrqOMkTSx6z6gnf1KXGVziO0ew7sKJ0koCODqFUMlcUNLx/WvEuvI5EJZtZ0Co3IYJGZWmShoix1KBLXVAjYNQICAb1IrNYzoVk5qPUYoI53WQC0E4haBVNR1FCYW8+uFrJrBCQSobZSypOBZWdna2trS5xl5nvQmSQyhcBgkVlaZLNuDJiLFnQ0ELWgmZCQEF9fXzc3N7wLAUCpQOMCAABkDqIWAABkDqIWNKOrqytxghgAwPeAqAXNlJaWCgQyWXcHgI4MohY0Q6PRCARYFRwAKYOoBc1wuVwYlAKA1EHUgmaYTGZLk3YDANoMXlSgmdraWpFIhHcVACgbiFrQjIGBgcS1cwAA3wOiFjRTVFTE5/PxrgIAZQNRC5pRUVGBEQgASB1ELWiGz+fDCAQApA6iFgAAZA6iFjSjr68Pl8UAkDqIWtBMcXExXBYDQOogagEAQOYgakEzdDod7hYDQOrgRQWa4XA4cLcYAFIHUQuagflqAZAFiFrQDMxXC4AsQNQCAIDMQdSCZmBqcABkAaIWNANTgwMgCxC1AAAgcxC1AAAgcxC1oBmYAwEAWYCoBc3AHAgAyAJELQAAyBxELQAAyBxELWgGxtUCIAsQtaAZGFcLgCxA1AIAgMxB1IJmNDU1SSQS3lUAoGwgakEzlZWVQqEQ7yoAUDYQtQAAIHMQtaAZWO0GAFmA1xVoBla7AUAWIGpBMwYGBjAHAgBSB1ELmikqKoI5EACQOoha0Aws4wiALBDg1iCAEPLy8qLRaGKxuKKigsFgYP9XUVE5f/483qUBoAyg/QIQQkhDQ+Pdu3fY/3k8HkJILBYHBQXhXRcASgI6EABCCI0dO5ZKpTbdYmxsPG7cOPwqAkCpQNQChBDy8/MzNjZu/FIsFg8cOLDpFgDA94CoBQghRCaTx4wZ09iwNTY2njBhAt5FAaA8IGrBJ/7+/qampo1NWkNDQ7wrAkB5QNSCT8hkckBAAIVCgSYtAFIHIxDwV13GryxpkIcbYu27DrUxT7Wzs6svZ70rZ+NdDqLQiDpGFBodJnUECg/G1eLpwxtOyq3K6jK+qTWjrlKAdzlyh0wh5L/hmHWjD52oTyDCMjxAgUHU4uZjVv396DLPicYqVOjGac3HbHZqXHnAfBMK/KKAwoLnLj5KP/LuRJUMn24KOftFxpYM51H653bn410IAG0Hr3N8pMRW9h+ph3cVCkPLgGpkSX+dXIN3IQC0EUQtPt5nctR1KHhXoUhUmeSS9zy8qwCgjSBqccDliJiaZAoNLqx/A3UdCpcDi54BRQVRiwMiEdXCeINvJBIiHkcOBsQB0CYQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQtQAAIHMQteCbCQSCCT/7HfxrF96FAKAwIGrBNyMQCEwmi0aj4V0IAAoDlnEEkonFYgJB8nJeJBLp4P7jMj0FAEoGWrWK4cOHvIWLZnn7uASOH77zz1CRSCQQCAa7O508daxxn+UrF8wOnowQepuVOdjdafPWtRMn+XsN6z91+ri4WzcadyssKli9JmS478DR/h5Llga/znyFbd+9Z6v/GK8HD+5N+NlvsLvTxeiowe5OV65eaHzgseOHvIb1f/Pm9WB3p8HuTkfCD2DbT546Fjh+uLePy9z501KeJmEbX2Wkz1swfai38yg/963b1tfUflpAYcq0wA0bl/9zImy0v8dw34F8Pr9dfn8A4AxatYph+46N79/nzpm9iMNhp6YlE4lE0ZdWMy8qKlj42wqBQHDp0tlNoavIZLKbq0d5ednceVONjU2D54QQCISYmKvzF0z/68CJTp0sEUJsdt2RowcWzF/G5dYPcHa9fj06Jvaqr48fdsDYuGuurh5mZhYbN/yxfsMybGPK06TDYfvc3Yf90Mc56cmDeg4HIZSb+25RyCwLC8sli9dWV1UePfZXSUnRjj8OYg958uQhl8cN/f1PTj1HRUVFxr85AOQCRK1iKCoq6NqlG5Z6gWMnfM1Dxgf+7GDvhBBy7N13yrTAU6eOubl6nIgI09TQ2rH9IJlMRgh5egyf8PPoK9cuzJ0TghBqaGgIWbjKxsYOO4KPj9+u3VuKigoNDAxfvnxeUJC/fOl6Go3mMsCt8YN/UVEBQshvVGD37j09PYdjGyMijxCJxG1b9zHVmAghJpMVumXNs2dPe/XqjRAikcmrV4aqqqrK7LcFgNyBDgTF4Okx/Enyoz17t1VWVnzrY4lEopNTv7dZmXw+//HjxHc5WcN9B3oN6+81rP9w34HFxUWlJcXYnjQarTFnEULuQ4bRaLS4W9cRQjGxVzt3trKz6/Wfg/f7wYXJZIVuXv3o0f3GjWnPUhwc+mA5ixDq06c/QijzzaeeChsbO8hZ0NFAq1YxTJ82R1NTKyIy/PqNSzN+mec3OvCbHs5UY4rF4npufUVlef/+A2dMn9v0uwyGGvYfVVV60+1qampDBg+Nu3V9XODE23dip02d/fmRtbV19u0J339w5/KVC+zseq1ZtVlXV4/NrtNQ1/zf2ZkshFBZWemns9AgZ0GHA61axUAgEMYE/BR5InqAs+uevdtevEj7pmv3paUlNBqNxWQxmazq6iozM4um/7S1dVp6oI+PX15ezomIMIGA7+HuLXEfMzOLrZv37PjjYE5O1tZt6xBCOjp6NTXVjTtgLXG1/2/kAtABQdQqBh6PhxBiMBiTJ89CCL15+5pEIjGZrLLyT01FsVhcUlIk8bG1dbUJCfF23XshhHr37pue/izzTUbjd+vr61s5r62NnZVl14jIcA93bwaDIXGfhoYGhFBvhz79+g188/Y1Qqh7955pz1K4XC62w717txBCPXrYf8cvAADFBh0IimHdhqVqDDUnx36PHt9HCFl3tUEI9e3TPzbmam+HPlqa2meiIt6/z+3SpVvjQyJOhpeVl9bXcy5dOsvmsKdMnoUQmvTzjEeP7i9eMidw7ARNTa2kpAdCkfD3DTtaObWPj9/uPVtHjAiQ+N2M1y/Xb1g6elSgqio9KelBN2tbhNCEn6bGx99cunzuCN+AkpKi4/8ccrB3su/lKP3fCwAKAqJWMdh0s7sZc+VeQryOjt6ihSuxy1NzZi/i8Xhbtq5lMNRGjhjD5XGbfmxXU2OePHm0vKKscyerTb//aWvbAyFkbGSyb0/4wb93RZ4MJxAIXbp08xs9rvVTe7h7JyTEd7GylvhdigrF3KzTyZNHxWJxL3vHecFLEEImJmbbtuw7FLZ32/b1qqp0T4/hs2YugLsVQEdGEIvFeNfQ4TRwRcfW5/64rLOMjv82K3PGzKDQ3//s33+gjE7R/vLfcLJSq0bMMMK7EADaAvpqAQBA5iBqAQBA5qCvVgl1sbK+fSsZ7yoAAP8DrVoAAJA5iFqgMHLzcjds2FBXV4d3IQB8M4haoDAMDY169eqF3RkxcuTIadOmYXd2FBQU4F0aAF8AfbVAYVAplBGjRmH/P3/+fHp6OjZWd968ebW1tTdv3mxoaHjy5EmPHj1YLBbexQLQDLRqgUIik8n29vYUCgUhdPbs2dOnT2N3J58+fXrGjBkIofLy8sjIyMzMTLwrBQBB1AIloaGhgRCiUql79uz5999/EUKqqqrFxcXY/zMzM0NDQx8+fIh3maDjgg4EoJCqq6sLCwtLSko+fvz4448/fr4DnU5fuHAh9n8zMzNra+ucnJz+/fsnJCScOHEiICBg6NChbDa7pTl0AJAuiFqgMMrLyzdtOlpUVPTx40fs4hibzWaz2QcOHEhISGjlgaqqqgEBn6bLcXZ2ptPp2MMTEhJ27Ngxd+7ckSNH5ubmamlpQScvkBGIWqAwyisqLty+IBKJCARC4+Q1YrG49Zz9DxKJ5Oj4aY6xYcOG9e3bt6qqCiH09OnTvXv37t27187O7tKlS+bm5r16/XfJCQDaDPpqgcLo2qWLvb09iUSS4iRhWlpanTt3Rgj5+/vfvn3bysoKIVRcXLx7925sAO/OnTsvXbokEAikdUbQMUHU4oBAJOgYUvGuQuGIWVoqYWFhffv2/U/U/vbbb1evXsXG2H4nGo2GEPrll1/Cw8PV1NQQQl26dElNTa2urkYIbdu2bc+ePY1TngPw9Ujr1q3Du4YOh0QiJMdVGlrSaXQS3rUojKy0WjqTaGyp6uPj8+zZs8LCQmz+z5SUFDqdnpCQsHbt2rKyspqaGgsLCxJJar9Ya2trNzc3Op2ONYHLy8u7dOlCo9G8vb3fvXvn6urK5XKFQiG2AjEALYH5avHx8Go5lU7u0lsd70IUxp0zhT8M09QzpWFfzp49+8mTJ2KxODn5fxPrPHz48Nq1a7GxsQEBAT179vTw8JBi5v5HSUnJ27dvBwwYUFRU5O/vP2DAgO3bt9fU1BQXF3fp0kVGJwWKC6IWB8nJyWw2++OjTo5eOkad6V/xiI4u8WKxrgnF0V2z6cbZs2dnZ2ffvHlTwv6JiVevXo2Li3N1dfX29h48eLCs14DIy8szNzcvLS2dO3euSCQ6c+ZMXV1dUlKSg4ODpqbmVxwAKDmI2vZ2586dU6dOrVmzxtDQ6N/tHyx7MZlaFC0D6LqVQMAXlubz3r9mW9jSew1syyeA+Pj4J0+eREVFubu7e3p6enh4yKDM/+Lz+SoqKhwOZ+3atQKB4M8//8zIyEhKSnJ1dbWwsGiHAoAcgqhtJ8ePH3/69Onu3burq6vV1f+XGs/uVb3P5CBEKC+QwlWdr1FfX6+qqtrSd/l8PolIJMrsc/c30dSn0Jkkm75Mky7f2/aPi4uLjY2tqqrS1NT09PR0d3eXUo1fBbtLmMViTZ48OSYm5smTJ6NHj+7evXt71gDwBVErWyUlJRQKhUqlHj58eNKkSU1Dtv0JhcKZM2e+fPkyJCSkcUj/f4SEhPj6+rq5ubV7de0kNjY2Njb29u3bHh4e3t7egwYNaucCqqqq4uPjWSyWh4fHyZMnExMTJ0+e3KdPn4aGBmxKB6CUIGpl6MSJEydPnjxz5gyTycS7FlRYWDhv3rzs7GwCgTBnzpypU6dK3C05OdnExMTAwKDdC2xXIpEoLi7uyZMn0dHRHh4enp6egwcPbv8y+Hx+SkoKmUx2cnKKiIg4f/78woULXVxcSkpK9PT02r8eIDsQtdJ39+7dwsLC8ePHp6Wl2dvb410OQgg9f/58zZo1+fn52Jfjxo1bvHgx3kXJBaFQiPUt3Lt3b9y4cY6Ojji26PPy8gQCgaWl5ZEjR44cObJnzx4nJ6eXL18aGxtj8+kAxQVRK2Xp6enh4eGLFi0yNjbGu5ZPbty4sWvXrrKyMuxLsVjs4eGxdetWiTtfuHDB1tbW2tq6fWvEn1AovHv37tWrVxMTEz09PT09Pdu/b6EpHo/HZrO1tLTCwsJOnTq1e/duOzu7O3fuGBsbw2AyRQRRKx3//PPPpUuXzp49y+VysTuO5MeQIUNqamoavxSLxY6OjocOHZK48+LFi729vYcMGdKOBcoXPp+P9efW1dUZGhp6eXm5uLjgXRTCnlfHjx+/fv16aGho586dT548aW5u7uzsLOtxbEAqIGq/y4cPH4hEorGxcWRk5JgxY6hUeRyzNWrUqPz8/MYXpEgk6tGjx/HjxyXunJ+fz2KxYIIrrF0ZFxcXExOTnJzs4eHh5eU1YMAAvItC2JslgUCIiopKSEhYvXq1rq7u9u3bu3btOnLkSIhduQVR23bR0dFHjx4NDw/X0tLCu5Yv8/T0rK6uFolEYrG4c+fOUVFReFekMLhcLpa5tbW1FhYWnp6ezs7OeBfVzNWrV1NSUpYvX04kEkNCQpycnIKCgvAuCjQDUfvNEhMT09PTZ86cmZWVhU0EpRCCg4NXrlxpaGjo7u6uoqJy48YNibtduHDB3Ny8d+/e7V6gAuByuTExMbGxsc+ePfPw8Bg6dOgPP/yAd1H/de/evTdv3kyfPr2kpGTJkiWDBw+eNGmSQCCAWRrwBVH7DQQCQWVl5caNG+fPn29paYl3Od/gwYMHp06d2rt37xf33L59u6mp6fjx49ulLkXFZrPj4uJu3ryZlZXl6urq5eXVp08fvIuS4MWLFzk5OSNHjszIyFi1alVAQMBPP/3E4XCw2XNAe4Ko/Sp3797duXNnVFQUkUhUxNbB0qVLfXx8vuaS+vv374lEoomJSbvUpfDq6upiYmLu37+flpbm6enp7e0tJ8P7Ppebm1tUVNSvX7/ExMR169ZNnjw5KCjoP/cuAtmBqP2C3NxcCwuLI0eODB06VEEDqKysLCgoSOK0LEBaqqurY2Njnzx50pi58nzfbUVFRWFhYffu3R88eBASEjJnzpygoKCSkhJdXV24sCYjELUtSk9PDw4OPnDggK2tLd61fJfDhw8LhcJZs2Z9zc7Pnj17/vz5xIkTZV+XciorK4uNjX358uXz58+HDRvm4+Njbm6Od1Gt4fF4Hz9+7Ny5861bt5YuXbp69epRo0bl5OQYGhrK27BFhQarMEhw9+5drD/u8uXLip6zCKGMjIyWZjz4HIVCgfbv99DR0fnxxx9///33gwcPUqnUgwcPTpo0KSoqisPh4F2aZFQqFVvyx93dPTk5GbvQl5yc7O7unpSUhBBKS0tjs9l4l6nwoFXbTF1dnbe395IlS0aMGIF3LdJx//79uLi4r19rQyAQPH78WE4GkCqH9PT0K1euZGRkGBsb+/n5yecFNIlqa2uZTOauXbvOnz8fHh5uZWWVlpZmZWWFLQUEvglE7SdRUVFeXl4EAoFMJivT9dmZM2f+8ssvTk5OeBcC0M2bN2NiYrKyssaMGTN27FjF+niO3a62d+/es2fPHjlyxMrKKjU11dbWVj5v25FDELUIIRQaGkokEpcuXapk1wRev3599OjRlqY7aMmhQ4ecnZ3t7OxkVleHlp+ff/bs2bdv3xoYGAQFBWEf3hULFrtbtmy5dOnS1atXNTU1k5OT4e28dR06ah8/fpySkjJ79mxlHfKyYsWKgIAAR0fHb3pUREREaWnpb7/9JrO6AEIIXbx48caNGxQKZerUqXI7ROyLsCUnZs6cWVZWdu7cuY8fP9bW1nbr1g3vuuROB41aoVBYVla2fv36tWvX6uvr412OTMTGxt66dWvLli3f+kA2m/3hwwd4tbSPxMTE8PBwTU3NKVOmyPP4sC8SiUREIjEvL2/FihUsFuvgwYM1NTX19fXK+vr6Vh0xardt2zZjxgwqldrKui9K4Ndff929ezdM7K8QUlJSdu/ebWNjExwcLA8TyX+nuro6NTW10tLSSZMm9ezZc8uWLVVVVR18yt0ON9hr+/bt5ubmGhoayp2zc+fOnThxYptzNjIy8vLly9IuCrTI0dHxn3/+6d+//4gRI5RgJiBsiIKuru61a9fmzp2LzYHXr1+/6OhohFDTKT07jo7Sqi0rK4uOjp42bRr2MQfvcmQrIiJCKBROmjSpzUfIz8+fM2cO9sIA7WzPnj3FxcWbNm3CuxAp4/P5eXl5VlZWW7ZsKSsrW7BggYLeftk2HSVqfXx89u/f3xGWhg4PD8/Ly1u/fv13HkcgEBCJRKV/W5JPqampwcHB8fHxyjqUKjMzk06nm5qabt26lUAgzJgxQ+m7F5Q/alNTUx0cHPCuop3ExcUVFBT8/PPP338oLpdbXFws5zeVKjEulztx4sTDhw8rdwZVV1ffuHHD2tra3t4+LCzMyclJcQdjtE6Z2yw8Hs/V1bXjhEViYuK1a9ekkrMIIRqNdu7cucjISKkcDXwrGo0WFRW1ePHi+vp6vGuRIXV19XHjxmHxam5ufuTIEWw5y1evXuFdmpQpc6s2OztbX1+/g9xEGBkZWVhYGBISIt3Dnjx50t/fX7Hua1Im6enpZ8+e/fr7qpVDUVHR4sWLTU1NQ0NDlWZSc+WM2ry8vNTU1NGjR+NdSDvZtm0bmUxeuHAh3oUA6Vu+fLmfn1/fvn3xLqS9FRQUGBkZJSYmRkdH//bbb4aGhnhX9F2UsAPh9evX27Zt6zg5u3DhQnNzc9nlbEpKyqJFi2R0cPBF5ubmz549w7sKHBgZGSGEBgwYMHTo0BcvXiCEYmJi8C6q7ZQwart167Z//368q2gPb968GTBgQFBQ0Lhx42R3FkdHx19//TU2NlZ2pwCt0NPTKykpwbsKPLm7u3t5eWHz9Lu4uFRVVeFdUVsoW9Tev3+/trYW7yraQ3R09Nq1a2/duvWtUxy0gZWVlaen5+PHj2V9IvA5Q0NDFRUVvKuQCzNmzMDe8quqqo4fP453Od9GqaL29u3bFy9eVIL7Gr9o0aJF+fn5p06das8LVp06dRo1alS7nQ577bjjAAAczklEQVRg7ty5o4izf8mIqqqqhoaGhoZGdXV1WFgY3uV8A6W6LJaZmWlubq7cl8tfvXo1Y8aM33//3c3Nrf3Pnp+fz+PxOs64DnkwYsSIv//+G+u4BE1hczmeOXMmMDAQ71q+TKlatdbW1sqds/v27Ttx4kRsbCwuOYsQMjExsbS0LC4u3rlzJy4FdDTPnj2zsbGBnJUIe7H36NFj7NixeNfyZcoTtYWFhRs3bsS7ClnJzc0NCAhgMBibN2/GfaIcS0tLfX39W7du4VtGR7Bhw4bZs2fjXYVcs7GxOXHiBN5VfJnyRC2Xy1XWMTFnz55dtGjRjh07pkyZgnctnwQFBWHr/WG39wBZOHfunJubW0eYuOM7Yc3bpUuXFhcX411Li5Qnao2MjEJDQ/GuQsry8/ODgoLYbPa5c+fk7SWHddfSaLTFixfjXYsSysrKOnPmDDYDIfgaGzZsaMNE+O1GqS6LKZmjR49evHhx69atcr4gQmVlpaamZkxMjIeHB8wEJhVisbhPnz7Jycl4FwKkRqleGBs2bHjz5g3eVUhBdnb28uXL2Wx2dHS0nOcsQkhTUxMhZGpqOnv2bAUdXi5vZs+erdB3RuFFKBTK7XhbpYpaOp2ekpKCdxXfa9++fcuXL586dWpwcDDetXwDGxubv/76i8fjIYRycnLwLkeBjR49ev369VpaWngXonhIJNKFCxc+fPiAdyESKFXUTp48uU+fPnhX0XZpaWk+Pj4MBuPMmTNdunTBu5y2wNbsW7x48blz5/CuRSGNHTt27969enp6eBeiqGbOnNnQ0IB3FRJAX6282LhxI4fDmT9/voGBAd61SEFMTIyXl1d2dralpSXetSiGoqIiPz+/8+fPK/oUVkAipWrVIoSmTJmCfYZVILdv3+7Xr1+PHj02b96sHDmLEMLmB8nIyJg/fz68nX9RamrqtGnT7t69Czn7ne7cufPu3Tu8q5BAGebcbYpEIo0cOVIgEFRVVZmZmV24cAHvilpTXl6+fv16XV3dhIQEpZxSxNfXV0NDg81mV1VVdag1+77J8ePHc3Nzr169inchyiAmJsbV1VUOZ41QkqgdNGgQh8PBWk8EAgHb2Lt3b7zras3Ro0dPnTq1du3aAQMG4F2LDLm4uGArFvv7+x86dEhHR6fxW+7u7p6ensuWLcO1QJyFhISYmZmtXbsW70KUhIODg3zex6wkHQjY65lAIDTmLJVK7devH951SZaWljZr1iw2mx0TE6PcOdvIwsLizz//TE1NbbqxqqoqLi7u+fPn+NWFp5ycnNmzZ/v4+MybNw/vWpTH2LFje/TogXcVEijPZbHAwMCsrKzGIfT6+vqHDx+Ww/e3DRs25OXlrVu3ztTUFO9a8DFixIiFCxeuWrWKx+OJxWJbW1uFuIdduq5cuXLs2LFDhw7BoC7pSktL09fXl8MubyVp1SKENm/ebGxs3Pilvr6+vOXspUuX+vbt26tXryNHjnTYnEUIXb58ef369djVSwKB8Pbt26NHj+JdVLtasmRJfn7+2bNnIWel7syZM/L5OUl5otbS0nLWrFnYvOAikUiuVpN///79tGnTUlNTk5KSYHZthFBNTU3j/wUCwdmzZ+V5ohApysjIcHNzGzp06KxZs/CuRTl5eXnJ55h0JbkshvHx8UlPTz937hyDwZCfFUb37Nlz+/bttWvXylX648jb2/s/UyUUFRVt2bLlzz//xK+o9nD8+PHY2NjLly93hIVC8ILXVM5f9FVRK+CL6utEsi9GCmbPWPTuTWFlZWUnU9vaSgG+xbx69WrTpk3+/v7/hEchhNpWj1iMWFoK9o5YVyVo5RIAlaShr80QCoV8Pl8oFGKfQl49z7kQdcPDw6NdC21Hq1ev7ty588G9x5Cgjc8EDJVGpKgqz4dRqfvw4QOTydTQ0MC7kP/6wmWxjKSa5wnVFUUNqmqkdqzqu4jF4sZxCPji8XgUCuU7i9E2on58y7GyV3MeoU1nynvm3j1X+vZprZ45raKwtZsjhUKhWCxGYrEYIbFIJBKLkVhMw3vKc9kRCAQEAiKRpPDnI5IISCzuNUi9l6umNEpTEr179yYQCE2He4rFYiMjoytXruBd2iet/e2TYirKCvgD/Q2YWko4ul6B8BtElcW8yM3vx4WYsuT1b8FvEIWvyR3or2frrEWjK8wbsyKqreBnJFXeOVvqNkYX71rkhbOz88OHD5t2TKmoqIwfPx7Xoppp8ZPI4xsV1aWCgX76kLO4U6EQ9UxVxy/t/O8fH+rZQrzLkeyfjXmj5piaWqtBzsoaU0ul7zA9sgox/nQJ3rXIiwkTJqirqzfdYmRkJFdrjkmO2sqShrKPvH6+ML2QfBk83vDB5TK8q5AgKabCYYgWgwXvyu2nl5u2gI8K3tXjXYhc6NevX9OZnUkk0pgxY6hUKq5FNSM5ass+8sRiuejuBE1p6FLevWDjXYUE+W/q1TQhZ9sbSYVQ8kHBJleSncmTJ7NYLOz/xsbGY8aMwbuiZiRHbV21UNdUmVf5VlA0OknPVJVdjfPIis+RSAQNPTlqQXQQuiaqnBq5ezLgpW/fvljDlkwmBwQEUCgUvCtqRnLU8nkiPlcxRnd1NOUFXDkZX9FUeSEPwfOl3QkaRFw2/N7/Z8qUKUwm08DAQK56aTHyPngIAKCsit/XVxTxObVCrG3Ok0LzzmyQzRxdXd175yq/vzwGkywWi+ksMoNFMuykqqbxXWkJUQsAaFcF2fWZKbXZL9h0dQqRTCKrkIgqJCKZJJWZr+wdPRFCtRwpHKquniDk80V5DUgsunOujMEkW9kz7Pqz6Ky2xCZELQCgnZQX8BIulgvERIIK1by3kQpNYfJH1wrV1/DeZ3Ne3M/v4qDmMkqbSPq2fjyF+VEBAAot4UJ51vM6nU5aGrp0vGtpC1UWVZVF1emkWfah+uDibPef9Ls5fcNcFhC1AACZO70zX1VLrVNfZVj0SMtUXctUPfVuSVkBz2Wkzlc8AinVJIoAADkkFomPrc9lGmqy9JVqPjNDW72SQvToWsVX7g9RCwCQoSNrcg3t9OkaSjhOX8tMMz9XFBv5VVMtQ9QCAGTl/L6PhjY6VLp83U0gRTqdNKsqUdrdqi/uCVELAJCJJzEVJLoqQ0shL4J9PV1LnewXvMKcL0xGAVELAJA+LkeYcqtK3VD9K/ZVeKraanfOfWEeKIhaAID0JVwo07fqKJOX0zVoQhHxXXpdK/t0iKh9m5U52N3p4cOEb3qUUCh88SJNZkWBtrh2PXq0v0dxcRH2ZVFRYWFRQSs7SNHn5wItqangVxQLNU1YeBciwePk6JDVP9TUSHkyUp1OmumJta3s0CGitm2279i4c1co3lWAZigUKoOhhk22/7Eg/6cJIzMzX7W0gxRJPBdoSc5LNiJ1rBniaWrUknxeTQW/pR1kcgsDLqt7Sf2kDTyYCVSOYH9fD/dhHu7DsC1CgeDzlfGa7vD18vPfm5iYtbKDxHOBlrxNZTO0O0QvbVNMXfq7F3X2Laz5JrWonTItsJOFpYWF5fkL//J43KjTN9TU1FLTkg+H7cvOfqOpqeVg32f6tDna2joIoZOnjl2MPlNbW2NlZT150kzH3n0RQoVFBQcO7Ex5+phCoXbt0m3q1NndrG0RQi9epJ2ICHuRnoYQ6mbdfdasBdZdbRBCd+7Grd+wbOP6P05HnXj9+uWP4ydNnfIrl8s9ERF2+3ZMaVmJvr6hl6dP0E9TsApzcrP/PfNPZuYrExOz+XOX9ujR2lLhW7atu30nFiE02N0JIXQy8pKhgRFCqKWf6PqNSxcvnnmXk6WqSu/bp3/wnBANDU2E0Ko1i8xMLbg8bkzMFbFY3Nuhb4D/jxGRR9JfPtPS1J4yeZan53Bp/QkURWVlhf8YrxXLN3p6eCOEuFzuipULdu74C/tu/O2Yjb+viIyIfvMm4z9/35LS4ps3ryCEYm8+Ki0rmTRlDEJo/YZl6xEaOtR32ZJ1W7ata9yBTCavWrPI1MScTCZfuXpBwOf36+cyf94yNTU1hFB5ednefdtTUh6TVVQcHX+4d+/W3wcjOnWylFhwYVHB5+fCKg87sv9W/I2GBp6piXlg4MQhg70QQiUlxUeOHnj8OJHNrjM1Nf/pxylY+r/Nylzw2y+rV4YePrLv/ftcfT2DoKCpFRXlly6fraurdXDoE7JwFfa0UWgNXBGfJ9bRlsminA0N3OtxB1Of3+Tzebo65m4uQfY9PBFC9x6cSnsRN8j5x+txB2try4yNuo0dtVxP1wJ71MeCzIvXdn74+IrF1NHVbu099Xuo6dALc9n2rpK/K83PWU+ePHyd+TL09z83btihpqaW8jRpydJgC/POIYtWB46Z8Pz504Uhs7hcbsrTpMNh+3r27L1wwQoDfcN6Dgd76s+dN7Wmtjp4TsjMGfP4fP78BdNzcrIRQkVFBbwG3sQJ0yf9PKOoqGDZ8nlcLrfxpLv3bvUd7rdt674RvgFCoXDFygVnoiIGDhyyJGSN6yD3D/l5pP//IBMRecTBvs+C+csaGhpWrl5YV9daH/aEn6b2duhjaGC0Z1fYnl1h2lo6CKGWfiKE0KtXL8zMLGbOmDfC1z/xwd2t29c3HurUv8cRQjt3/D0u8Of7iXcWL50zYIDbnzsPWVlZb9m27v37XCn+CRSCpqaWvr5BYuId7MuEhPjUtOTX///Z/O7dOOuuNkaGxtiXTf++/n7jG9+ZtLV0Vq74HSE0ZfKsPbvCJvw0FSHUdAfMmaiIoqKC0E27gueE3LkbFxF5BOuFX7FywctXz+fPX/bj+El378bZ93JsKWdbOpdIJFq56reHD+8F/TTltwUrrKysN/6+4tr1aISQQCh4/frlqJFjfp25gMVS3xS6KuP1S+xQHA5n154tv0wL3rplL4VK3bZ9w+OkxNUrQxf+tvLp06T9B3fK5lfertjVAk6dTFbAE4lE4ZGLXr1OGDJoUsCoZcaGXSPOrHqccgn77vv89LuJkWNHrZj047aq6uJ/z2/AtheX5h4M/7WmpnS452xX558+FmbKojaEkAqV3MqQL2l2IJDI5NUrQ1X/f4npvfu2j/D1nzd3Cfalk1O/SVPGPEl+WFNTjRDyGxXYvXvPxhfGiYgwTQ2tHdsPkslkhJCnx/AJP4++cu3C3DkhHh7ejbtZW9suXDTrRXpaH6d+2Ba/0eOGDvXF/h9/OyY1LXlxyOrh3qM+L2/+3KXYnuZmnWYHT055+th1kHtLP4uJiZm6ukZFZXnTxm9LP9FAl8ELf1vR2H1BJpMjIsN5PB62tJG5ead5wYsRQl27dLt2/WI36+5+owMRQnNmL0q4fzvtWYqZmcV3/+4VjOsgj8tXzjU0NFAolOs3LiGErlw5383atr6+PunJg58n/tK4Z9O/r66unoV5Z+z/FAqla5duCCEzM4vGv1HXLt0ad8CYmJitWL6RQCDYdOt+7378k+SHs2bOz8hIf/P29do1W9xcPRBC79/nXr9xCStGYrUSz3UvIf75i9RTkZd1dHSxjov6es6586eGe48yMjQ+Fh6FPR+8vUf5BXgkJt6x6dYde+CsmQv69XNBCAWOnbB12/rf5i/v1MnSDvVKSXn8OClRBr/s9sapFapQZdJR++LV7ZzctBWLLqqzdBFCvXsO5TVw7j88/YPjSGyHKUF/sJjaCCGXfoGXb+xmc6oZdPWrN/cSCMS5M4+oMTQRQgQi8fzlbbIoj0wlcVt+j5Fm1NrY2DXmbFFRYV5ezsePH65cvdB0n5KSYjdXDyaTFbp59dzgxdhzDiH0+HFiSWnxcN+BjXvy+fzSkmJsVfeE+7fPREXk5eXQ6XSEUGVFeeNuvXv3bfx/0pMHVCp1qJevxPJYrE+dRxYWlgih0tKvup2uUSs/EVbt+Qv/xsZdKykpolJpIpGoqqpSX98AIUSl/G8lGAqFSlb5tAaXnp4+Qqi6+sv3mSgfN1ePM1ERT58mmZl3Sk1LHjkiIDbu2uxfFz5OSuRyua6uHo17Nv37tgGNSmt8C9TXN0xPf4YQKiktRggZGX2a+sTExEwkEtXXc75piZRHj+4LBIKfJoxs3CIUChkMNez/Wdlvjh3/G7uMJhQKK5o8YxufDyoqFISQyv+fVFdXTzmeDOxagQpNJlGbkZkoFAlCd/o1bhGJhKo0tcYvqZRP+aOpYYgQqqkpVSFTM7Me9e8TgOUsQohElNUcW0QSkaRC4HIENLqEU0jzrKq0//XOVFaWI4Qm/Txj0MAhTffR0tJRU1Pbtyd8/8Gdy1cusLPrtWbVZl1dvYrK8v79B86YPrfpztgT958TYUeP/RXg/+OM6XPLK8rWb1gmEv9vtna66v/uRamsKNfR1iV96dIndnlaKPy2zzit/ERisXjFygWZb15N+nmGrW3PhIT4f0//07RIibAI6JgXW2xs7PT1DRIf3M14nW5mZhE8J+ReQnz87ZvJyY+a9h785+/7nVTIKiKRECFkbGyKXQPA2qoZGek6Orrq6hrfdLTKynJtbZ2df/zVdCOJTEYIPU19snTZXAd7pyWL1zLojDXrFn/xyYA9H5TjyUBA6Ct+3LaorStnMXVmTdnfdCNRUnSSSSpYENfUlgmFAi1NQ5kU9BmRUNzS6BdZBbyaGhMhxONxJX46NjOz2Lp5z9PUJ2vWhmzdtu6P7QeYTFZ1ddXnO/N4vJOnjvoMHx08Z1FjE7KVk1ZUlreyw7dq+tRv5SdKS0tJeZq0csXv2NWPj/nvpViDsho00P1W/A0ymRw4dqKKispw71EXLp4uKMhv2nsgI9Zdbfo49Tt0eE9xcWFVdWXig7urVm761oMwmayqqkp9fcPPV8A+cSLMyMgkdNMurDesaROkI6AzyYIGmfTV0lVZdexKTQ1DFZWvXTMUa8zW1Ulh/ZsvEglFSIwoNMlRK6txtSYmZvr6BtdvXKqv/9RPLBAI+PxPg84aGhoQQr0d+vTrN/DN29fY58T09GeZbzIaj4A9kMut5/F4XbvaYBura6qw3nGJJ3Vw6FNfX38r/mbjFoGg7euJ0miqFRXljedq5SfCqsKaSF8sEmDcXD0qKspraqqxDh9fX/+cnOz/9B60jkqlIYTKy0rbcPa5wYtNTMw+5OdpqGvu23vU7Usn/fxcvXv3FQqFly6fbdzS+MSorqmysuyK5WxDQwOnntOhngx0FknAk0nUWln2EYmED5LONW7hNXxh5gEajaGjbfrs5S2BoMURr9LC5wlpjBY/UsuqVUsgEObMXrRm7eI5cyePHDFGJBTejLni6Tl8TMBPGa9frt+wdPSoQFVVelLSA2xE16SfZzx6dH/xkjmBYydoamolJT0QioS/b9ihrq7RubPV+Qv/amlps+vqjv9ziEgkvnuXJfGknh7DL0af2bJ17evXL60su77LyUp5+vjQX5Ft+xF69ex9/calnX+G9rCzZzJZzs6DWvqJbG16UCiUw2H7fHz83r17e/LUUYRQzrssYyNlmAhZRmxs7PT09J0c+2GjrwwNjPr2da6qrGjae9A6PT19I0PjM2cjaKqqNTXV/n7jP29gSiQQCGYHTxo7ZoKxsSmBQKitramrq8PK+PpzeXoMv3zl/F9/7y4sKujapVtW1pv7ibePhZ+l0Wj29k43b16+dj2axVSPOhdZW1uTm5OtHJ0DX4OlSaaqyqQN59jL+3HyxSs391ZWFRobWhcUvX3x6s6SeacplNZmaPQaPP3k2bV7D03v29uXQCQmPDwti9oQQnyuwMiyxU8wMlyFYaDL4M2bdh099tf+AzsYDLWePRx69uyNEKKoUMzNOp08eVQsFveyd5wXvAQhZGxksm9P+MG/d0WeDCcQCF26dPMbPQ47zuqVoVu3rduwcbmJidmvv/6Wnf3m3LlTM2fM+/yMVCp1xx9/HT68Nzbu2pWr5w0MjAa7ebW5YevpOTzzzauY2KsPHyUMGzrC2XlQSz+Rrq7eqpWb9h/YsW79ku62PXfu+Pvosb/OX/jXxcXt+36FyoxAIAwa6O7e5HaDUSPG5Oa9+6YjrFoVum37+n37/9DTMxjs5mVg8FVdcmQy2cmx34mIsMbnBlONuWf3EQuLzi09ROK5tm/dfzhsb3z8zStXzpuYmI0cMQZryU6d/GtFednefduZTJavj3/gmAk7d4WmpiUzmfJ4o6rUkVSIDHVSbSmHKe2FbchklV8m7bkWsz/1eczDJxd0tc2c+/qTSF8Isd69htXX195JjLwSs1dft7O5qV1pWZ50C8PUlbK792kx9CX3xCfdrGjgol5uWrIoCHyPqB0540PM6Cz5uusxfE2O7wwzVaZ8VdUKoVCIXT4Vi8UFhR+n/zI+cOyEKZNn4V3Xt3n7tKaqmDtkvB7ehfxX+sPq9MdcA+uvXQxGOby9//6nJaYMdcnR36HXFjsctq9pX1sjFlM9MiIaj4pAe+DxeLODJ+npGfTq2VtFhfLiRSqXyzU2Nh0xSvKnkJkz5vv6+En8FpCoc3dG+sPWbhESiURrNntK/JYaXaOOI2HQW/dug34MWCutCuu5dZt2SBh9jxAyN+2R9+HF59sN9a3mTP+7xQPW8Aw7qbaUsx09agMDJ/r6+n++nUiAWXiUGYFA8PL0iY+/efTYXxQKpVMnq7VrtgwaOKRHDweJ+7OYHe52/u9EZ5ENzCjl76u1zST/6ohE4sLZJyR+SyDgk8kqn2+nUKQ5kINKobdUABITEEHCZ30SSUJVjcreVbgFtNYN0KGjVp2lrs6CV1GHQ6FQxgVOHBc48T/bsWkugFQMHK3z19LslqIWIaSliedvm0gkSrGAuvJ6OoNg2rW1vmlovgEApI9EJgwYqV1doAz3v30Rt7LWdYx26/tA1AIAZMLeVZOMGmpLWuu0VQLFmSXd+zF0jL6wJDBELQBAVnymGVZ+qKot/8KNBoqrKLPM0Jxs0+fLw/ggagEAMvTzKvO6gsraUjbehUhfSXa5tT11kJ/u1+wMUQsAkK0fl5iK6uuqC6vxLkRqhHzRh2eFVrYqDm5fO0sRRC0AQOZGzTQyNiO8vf++qrC1tQ4VQum7ireJH9z8tXoP+YYlMzr0YC8AQLtx8tC06ctMuFBekslBZApLj0FjfsMEwbirLeOwyzmVH2udvLTGzmnxHu6WQNQCANoJg0UeNkm/vJD35mld1rNSMSIQSUQylUwik0gUsrzNyEMkEfj1fCFfgBCqLODom9NsnRh283Tbth4zRC0AoF1pG1L7+1D7+2jXlPPLixo4NQJ2jVAkFPJ58hW1qmokApHMUKcymGTDzvpkle/qboWoBQDgg6WtwtJu7W5XZSI5aik0gggR2r0Y8GU6xjSJN2jjS8eYRiDJXVVKj6xCUFVTmNnUOjjJTWKmpkppntKOOlZc9XWC0nwunSl3n0VEQlFFYQPeVXQ4JR+4DHWIWsUgOWr1TKkEaNTKn8riBsterS0WgBezbvTaCpkvKAL+Q8AX6Zt/4X5QICdabNUaW9HunStq93pAa+IiCwaOlsfplnsP0cx4VFXyAT4JtZ+kG6Vq6iR9M4haxdDaesgvH1a/Tavr5aqtqU8hkeFmB9ywq/lVpQ23IgunbrRQZchd7wFGJBT/synPfrCWjjFNXVuRxksqFrFYXFbAe51UqWtE7eMF66QojC8sPZ/zkp12t6ooh0siQ4cCPvTMqJXF/M49GQNH6xCJ8v5XeHSt/G1qnZoGuTSfh3ctykmFQmSok3oNUrd26hArlSmNL0RtI159B1pdWa6IxWIaXcEuffB5HWo17nZFpRFhcJAi+tqoBQAA0GbQAwsAADIHUQsAADIHUQsAADIHUQsAADIHUQsAADIHUQsAADL3fysrIg7gCgFpAAAAAElFTkSuQmCC", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(super_graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 22, "id": "6b8badbf-d728-44bd-a2a7-5b4e587c92fe", "metadata": {"ExecuteTime": {"end_time": "2024-05-15T08:19:55.796497Z", "start_time": "2024-05-15T08:19:55.796497Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'supervisor': {'next': 'research_team'}}\n", "---\n", "{'research_team': {'messages': [HumanMessage(content=\"**AI Agents Overview 2023**\\n\\nAI agents are sophisticated technologies that automate and enhance various processes across industries, becoming increasingly integral to business operations. In 2023, these agents are notable for their advanced capabilities in communication, data visualization, and language processing.\\n\\n**Popular AI Agents in 2023:**\\n1. **Auto GPT**: This agent is renowned for its seamless integration abilities, significantly impacting industries by improving communication and operational workflows.\\n2. **ChartGPT**: Specializing in data visualization, ChartGPT enables users to interact with data innovatively, providing deeper insights and comprehension.\\n3. **LLMops**: With advanced language capabilities, LLMops is a versatile tool seeing widespread use across multiple sectors.\\n\\n**Market Trends:**\\nThe AI agents market is experiencing rapid growth, with significant advancements anticipated by 2030. There's a growing demand for AI agents in personalized interactions, particularly within customer service, healthcare, and marketing sectors. This trend is fueled by the need for more efficient and tailored customer experiences.\\n\\n**Key Players:**\\nLeading companies such as Microsoft, IBM, Google, Oracle, and AWS are key players in the AI agents market, highlighting the widespread adoption and investment in these technologies.\\n\\n**Technological Innovations:**\\nAI agents are being developed alongside simulation technologies for robust testing and deployment environments. Innovations in generative AI are accelerating, supported by advancements in large language models and platforms like ChatGPT.\\n\\n**Applications in Healthcare:**\\nIn healthcare, AI agents are automating routine tasks, allowing medical professionals to focus more on patient care. They're poised to significantly enhance healthcare delivery and efficiency.\\n\\n**Future Prospects:**\\nThe future of AI agents is promising, with continued evolution and integration into various platforms and ecosystems, offering more seamless and intelligent interactions. As these technologies advance, they are expected to redefine business operations and customer interactions.\", additional_kwargs={}, response_metadata={}, name='research_team', id='5f6606e0-838c-406c-b50d-9f9f6a076322')]}}\n", "---\n", "{'supervisor': {'next': 'writing_team'}}\n", "---\n", "{'writing_team': {'messages': [HumanMessage(content=\"Here are the contents of the documents:\\n\\n### AI Agents Overview 2023\\n\\n**AI Agents Overview 2023**\\n\\nAI agents are sophisticated technologies that automate and enhance various processes across industries, becoming increasingly integral to business operations. In 2023, these agents are notable for their advanced capabilities in communication, data visualization, and language processing.\\n\\n**Popular AI Agents in 2023:**\\n1. **Auto GPT**: This agent is renowned for its seamless integration abilities, significantly impacting industries by improving communication and operational workflows.\\n2. **ChartGPT**: Specializing in data visualization, ChartGPT enables users to interact with data innovatively, providing deeper insights and comprehension.\\n3. **LLMops**: With advanced language capabilities, LLMops is a versatile tool seeing widespread use across multiple sectors.\\n\\n**Market Trends:**\\nThe AI agents market is experiencing rapid growth, with significant advancements anticipated by 2030. There's a growing demand for AI agents in personalized interactions, particularly within customer service, healthcare, and marketing sectors. This trend is fueled by the need for more efficient and tailored customer experiences.\\n\\n**Key Players:**\\nLeading companies such as Microsoft, IBM, Google, Oracle, and AWS are key players in the AI agents market, highlighting the widespread adoption and investment in these technologies.\\n\\n**Technological Innovations:**\\nAI agents are being developed alongside simulation technologies for robust testing and deployment environments. Innovations in generative AI are accelerating, supported by advancements in large language models and platforms like ChatGPT.\\n\\n**Applications in Healthcare:**\\nIn healthcare, AI agents are automating routine tasks, allowing medical professionals to focus more on patient care. They're poised to significantly enhance healthcare delivery and efficiency.\\n\\n**Future Prospects:**\\nThe future of AI agents is promising, with continued evolution and integration into various platforms and ecosystems, offering more seamless and intelligent interactions. As these technologies advance, they are expected to redefine business operations and customer interactions.\\n\\n### AI_Agents_Overview_2023_Outline\\n\\n1. Introduction to AI Agents in 2023\\n2. Popular AI Agents: Auto GPT, ChartGPT, LLMops\\n3. Market Trends and Growth\\n4. Key Players in the AI Agents Market\\n5. Technological Innovations: Simulation and Generative AI\\n6. Applications of AI Agents in Healthcare\\n7. Future Prospects of AI Agents\", additional_kwargs={}, response_metadata={}, name='writing_team', id='851bd8a6-740e-488c-8928-1f9e05e96ea0')]}}\n", "---\n", "{'supervisor': {'next': 'writing_team'}}\n", "---\n", "{'writing_team': {'messages': [HumanMessage(content='The documents have been successfully created and saved:\\n\\n1. **AI_Agents_Overview_2023.txt** - Contains the detailed overview of AI agents in 2023.\\n2. **AI_Agents_Overview_2023_Outline.txt** - Contains the outline of the document.', additional_kwargs={}, response_metadata={}, name='writing_team', id='c87c0778-a085-4a8e-8ee1-9b43b9b0b143')]}}\n", "---\n", "{'supervisor': {'next': '__end__'}}\n", "---\n"]}], "source": ["for s in super_graph.stream(\n", "    {\n", "        \"messages\": [\n", "            (\"user\", \"Research AI agents and write a brief report about them.\")\n", "        ],\n", "    },\n", "    {\"recursion_limit\": 150},\n", "):\n", "    print(s)\n", "    print(\"---\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}