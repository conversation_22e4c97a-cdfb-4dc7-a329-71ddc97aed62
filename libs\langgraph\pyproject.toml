[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph"
version = "0.6.0a2"
description = "Building stateful, multi-actor applications with LLMs"
authors = []
requires-python = ">=3.9"
readme = "README.md"
license = "MIT"
license-files = ['LICENSE']
dependencies = [
    "langchain-core>=0.1",
    "langgraph-checkpoint>=2.1.0,<3.0.0",
    "langgraph-sdk>=0.2.0,<0.3.0",
    "langgraph-prebuilt==0.6.0a1",
    "xxhash>=3.5.0",
    "pydantic>=2.7.4",
]

[project.urls]
Repository = "https://www.github.com/langchain-ai/langgraph"

[dependency-groups]
dev = [
    "pytest",
    "pytest-cov",
    "pytest-dotenv",
    "pytest-mock",
    "syrupy",
    "httpx",
    "pytest-watcher",
    "mypy",
    "ruff",
    "jupyter",
    "pytest-xdist[psutil]",
    "pytest-repeat",
    "langgraph-prebuilt",
    "langgraph-checkpoint",
    "langgraph-checkpoint-sqlite",
    "langgraph-checkpoint-postgres",
    "langgraph-sdk",
    "psycopg[binary]",
    "uvloop==0.21.0beta1",
    "pyperf",
    "py-spy",
    "types-requests",
    "pycryptodome",
    "langgraph-cli[inmem]",
]

[tool.uv]
default-groups = ['dev']

[tool.uv.sources]
langgraph-prebuilt = { path = "../prebuilt", editable = true }
langgraph-checkpoint = { path = "../checkpoint", editable = true }
langgraph-checkpoint-sqlite = { path = "../checkpoint-sqlite", editable = true }
langgraph-checkpoint-postgres = { path = "../checkpoint-postgres", editable = true }
langgraph-sdk = { path = "../sdk-py", editable = true }
langgraph-cli = { path = "../cli", editable = true }

[tool.ruff]
lint.select = [ "E", "F", "I", "TID251", "UP" ]
lint.ignore = [ "E501" ]
line-length = 88
indent-width = 4
extend-include = ["*.ipynb"]
target-version = "py39"

[tool.ruff.lint.per-file-ignores]
"tests/bench/*" = ["UP006", "UP007"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = false
docstring-code-line-length = "dynamic"

[tool.ruff.lint.flake8-tidy-imports.banned-api]
"typing.TypedDict".msg = "Use typing_extensions.TypedDict instead."

[tool.mypy]
# https://mypy.readthedocs.io/en/stable/config_file.html
disallow_untyped_defs = "True"
explicit_package_bases = "True"
warn_no_return = "False"
warn_unused_ignores = "True"
warn_redundant_casts = "True"
allow_redefinition = "True"
disable_error_code = "typeddict-item, return-value, override, has-type"

[tool.coverage.run]
omit = ["tests/*"]

[tool.pytest-watcher]
now = true
delay = 0.1
patterns = ["*.py"]

[tool.hatch.build.targets.wheel]
packages = ["langgraph"]

[tool.pytest.ini_options]
addopts = "--full-trace --strict-markers --strict-config --durations=5 --snapshot-warn-unused"

[tool.codespell]
# Ignore words specific to the LangGraph library code
ignore-words-list = "infor,thead,stdio,nd,jupyter,lets,lite,uis,deque,langgraph,langchain,pydantic,typing,async,await,coroutine,iterable,iterables,serializable,deserializable,checkpointer,checkpointing,stateful,statefulness,prebuilt,prebuilt,supervisor,supervisory,swarm,swarming,multiactor,multiactors,subgraph,subgraphs,workflow,workflows,streaming,streamable,streamed,streamer,streamers,streaming,streamable,streamed,streamer,streamers"
