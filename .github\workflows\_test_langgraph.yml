name: test

on:
  workflow_call:

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
          - "3.13"

    defaults:
      run:
        working-directory: libs/langgraph
    name: "test #${{ matrix.python-version }}"
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: astral-sh/setup-uv@v6
        with:
          python-version: ${{ matrix.python-version }}
          enable-cache: true
          cache-suffix: "test-langgraph"
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        if: ${{ !github.event.pull_request.head.repo.fork }}
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_RO_TOKEN }}

      - name: Install dependencies
        shell: bash
        run: uv sync --frozen --group dev

      - name: Run tests
        shell: bash
        run: make test_parallel

      - name: Ensure the tests did not create any additional files
        shell: bash
        run: |
          set -eu

          STATUS="$(git status)"
          echo "$STATUS"

          # grep will exit non-zero if the target message isn't found,
          # and `set -e` above will cause the step to fail.
          echo "$STATUS" | grep 'nothing to commit, working tree clean'
