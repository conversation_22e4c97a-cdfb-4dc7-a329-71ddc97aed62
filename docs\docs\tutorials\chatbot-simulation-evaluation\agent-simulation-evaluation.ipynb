{"cells": [{"attachments": {"0ddf8d0d-ed93-456e-8898-116eea737aa1.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "a3e3ebc4-57af-4fe4-bdd3-36aff67bf276", "metadata": {}, "source": ["# Chat Bot Evaluation as Multi-agent Simulation\n", "\n", "When building a chat bot, such as a customer support assistant, it can be hard to properly evaluate your bot's performance. It's time-consuming to have to manually interact with it intensively for each code change.\n", "\n", "One way to make the evaluation process easier and more reproducible is to simulate a user interaction.\n", "\n", "With LangGraph, it's easy to set this up. Below is an example of how to create a \"virtual user\" to simulate a conversation.\n", "\n", "The overall simulation looks something like this:\n", "\n", "![diagram](attachment:0ddf8d0d-ed93-456e-8898-116eea737aa1.png)\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 1, "id": "0d30b6f7-3bec-4d9f-af50-43dfdc81ae6c", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain langchain_openai"]}, {"cell_type": "code", "execution_count": 2, "id": "30c2f3de-c730-4aec-85a6-af2c2f058803", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_if_undefined(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"Please provide your {var}\")\n", "\n", "\n", "_set_if_undefined(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "95c9332f", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>   "]}, {"cell_type": "markdown", "id": "6ef4528d-6b2a-47c7-98b5-50f14984a304", "metadata": {}, "source": ["## Define <PERSON><PERSON>\n", "\n", "Next, we will define our chat bot. For this notebook, we assume the bot's API accepts a list of messages and responds with a message. If you want to update this, all you'll have to change is this section and the \"get_messages_for_agent\" function in \n", "the simulator below.\n", "\n", "The implementation within `my_chat_bot` is configurable and can even be run on another system (e.g., if your system isn't running in python)."]}, {"cell_type": "code", "execution_count": 3, "id": "828479af-cf9c-4888-a365-599643a96b55", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "import openai\n", "\n", "\n", "# This is flexible, but you can define your agent here, or call your agent API here.\n", "def my_chat_bot(messages: List[dict]) -> dict:\n", "    system_message = {\n", "        \"role\": \"system\",\n", "        \"content\": \"You are a customer support agent for an airline.\",\n", "    }\n", "    messages = [system_message] + messages\n", "    completion = openai.chat.completions.create(\n", "        messages=messages, model=\"gpt-3.5-turbo\"\n", "    )\n", "    return completion.choices[0].message.model_dump()"]}, {"cell_type": "code", "execution_count": 4, "id": "f58959bf-2ab5-4330-9ac2-c00f45237e24", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'content': 'Hello! How can I assist you today?',\n", " 'role': 'assistant',\n", " 'function_call': None,\n", " 'tool_calls': None}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["my_chat_bot([{\"role\": \"user\", \"content\": \"hi!\"}])"]}, {"cell_type": "markdown", "id": "419340a3-5ecf-48e7-9028-4f2fad750502", "metadata": {}, "source": ["## Define Simulated User\n", "\n", "We're now going to define the simulated user. \n", "This can be anything we want, but we're going to build it as a LangChain bot."]}, {"cell_type": "code", "execution_count": 5, "id": "32c147df-7f90-4b0d-9a6b-671677020353", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_openai import ChatOpenAI\n", "\n", "system_prompt_template = \"\"\"You are a customer of an airline company. \\\n", "You are interacting with a user who is a customer support person. \\\n", "\n", "{instructions}\n", "\n", "When you are finished with the conversation, respond with a single word 'FINISHED'\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system_prompt_template),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")\n", "instructions = \"\"\"Your name is <PERSON>. You are trying to get a refund for the trip you took to Alaska. \\\n", "You want them to give you ALL the money back. \\\n", "This trip happened 5 years ago.\"\"\"\n", "\n", "prompt = prompt.partial(name=\"<PERSON>\", instructions=instructions)\n", "\n", "model = ChatOpenAI()\n", "\n", "simulated_user = prompt | model"]}, {"cell_type": "code", "execution_count": 6, "id": "6f80669e-aa78-4666-b67c-a539366d5aab", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hi, I would like to request a refund for a trip I took with your airline company to Alaska. Is it possible to get a refund for that trip?')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "messages = [HumanMessage(content=\"Hi! How can I help you?\")]\n", "simulated_user.invoke({\"messages\": messages})"]}, {"cell_type": "markdown", "id": "321312b4-a1f0-4454-a481-fdac4e37cb7d", "metadata": {}, "source": ["## Define the Agent Simulation\n", "\n", "The code below creates a LangGraph workflow to run the simulation. The main components are:\n", "\n", "1. The two nodes: one for the simulated user, the other for the chat bot.\n", "2. The graph itself, with a conditional stopping criterion.\n", "\n", "Read the comments in the code below for more information.\n"]}, {"cell_type": "markdown", "id": "65bc4446-462b-4ee8-b017-2862fbbdfaf5", "metadata": {}, "source": ["### Define nodes\n", "\n", "First, we define the nodes in the graph. These should take in a list of messages and return a list of messages to ADD to the state.\n", "These will be thing wrappers around the chat bot and simulated user we have above.\n", "\n", "**Note:** one tricky thing here is which messages are which. Because both the chat bot AND our simulated user are both LLMs, both of them will resond with AI messages. Our state will be a list of alternating Human and AI messages. This means that for one of the nodes, there will need to be some logic that flips the AI and human roles. In this example, we will assume that HumanMessages are messages from the simulated user. This means that we need some logic in the simulated user node to swap AI and Human messages.\n", "\n", "First, let's define the chat bot node"]}, {"cell_type": "code", "execution_count": 7, "id": "69e2a3a3-40f3-4223-9136-113738440be9", "metadata": {}, "outputs": [], "source": ["from langchain_community.adapters.openai import convert_message_to_dict\n", "from langchain_core.messages import AIMessage\n", "\n", "\n", "def chat_bot_node(state):\n", "    messages = state[\"messages\"]\n", "    # Convert from LangChain format to the OpenAI format, which our chatbot function expects.\n", "    messages = [convert_message_to_dict(m) for m in messages]\n", "    # Call the chat bot\n", "    chat_bot_response = my_chat_bot(messages)\n", "    # Respond with an AI Message\n", "    return {\"messages\": [AIMessage(content=chat_bot_response[\"content\"])]}"]}, {"cell_type": "markdown", "id": "694c3c0c-56c5-4410-8fa8-ea2c0f11f506", "metadata": {}, "source": ["Next, let's define the node for our simulated user. This will involve a little logic to swap the roles of the messages."]}, {"cell_type": "code", "execution_count": 8, "id": "7cad7527-ffa5-4c30-8585-b54a7a18bd98", "metadata": {}, "outputs": [], "source": ["def _swap_roles(messages):\n", "    new_messages = []\n", "    for m in messages:\n", "        if isinstance(m, AIMessage):\n", "            new_messages.append(HumanMessage(content=m.content))\n", "        else:\n", "            new_messages.append(AIMessage(content=m.content))\n", "    return new_messages\n", "\n", "\n", "def simulated_user_node(state):\n", "    messages = state[\"messages\"]\n", "    # Swap roles of messages\n", "    new_messages = _swap_roles(messages)\n", "    # Call the simulated user\n", "    response = simulated_user.invoke({\"messages\": new_messages})\n", "    # This response is an AI message - we need to flip this to be a human message\n", "    return {\"messages\": [HumanMessage(content=response.content)]}"]}, {"cell_type": "markdown", "id": "a48d8a3e-9171-4c43-a595-44d312722148", "metadata": {}, "source": ["### Define edges\n", "\n", "We now need to define the logic for the edges. The main logic occurs after the simulated user goes, and it should lead to one of two outcomes:\n", "\n", "- Either we continue and call the customer support bot\n", "- Or we finish and the conversation is over\n", "\n", "So what is the logic for the conversation being over? We will define that as either the Human chatbot responds with `FINISHED` (see the system prompt) OR the conversation is more than 6 messages long (this is an arbitrary number just to keep this example short)."]}, {"cell_type": "code", "execution_count": 9, "id": "28004fbf-a2f3-46b7-bde7-46c7adaf97fb", "metadata": {}, "outputs": [], "source": ["def should_continue(state):\n", "    messages = state[\"messages\"]\n", "    if len(messages) > 6:\n", "        return \"end\"\n", "    elif messages[-1].content == \"FINISHED\":\n", "        return \"end\"\n", "    else:\n", "        return \"continue\""]}, {"cell_type": "markdown", "id": "d0856d4f-9334-4f28-944b-06d303e913a4", "metadata": {}, "source": ["### Define graph\n", "\n", "We can now define the graph that sets up the simulation!"]}, {"cell_type": "code", "execution_count": 10, "id": "0b597e4b-4cbb-4bbc-82e5-f7e31275964c", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "from langgraph.graph.message import add_messages\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "\n", "graph_builder = StateGraph(State)\n", "graph_builder.add_node(\"user\", simulated_user_node)\n", "graph_builder.add_node(\"chat_bot\", chat_bot_node)\n", "# Every response from  your chat bot will automatically go to the\n", "# simulated user\n", "graph_builder.add_edge(\"chat_bot\", \"user\")\n", "graph_builder.add_conditional_edges(\n", "    \"user\",\n", "    should_continue,\n", "    # If the finish criteria are met, we will stop the simulation,\n", "    # otherwise, the virtual user's message will be sent to your chat bot\n", "    {\n", "        \"end\": END,\n", "        \"continue\": \"chat_bot\",\n", "    },\n", ")\n", "# The input will first go to your chat bot\n", "graph_builder.add_edge(START, \"chat_bot\")\n", "simulation = graph_builder.compile()"]}, {"cell_type": "markdown", "id": "2e0bd26e-8c1d-471d-9fef-d95dc0163491", "metadata": {}, "source": ["## Run Simulation\n", "\n", "Now we can evaluate our chat bot! We can invoke it with empty messages (this will simulate letting the chat bot start the initial conversation)"]}, {"cell_type": "code", "execution_count": 11, "id": "32848c2e-be82-46f3-81db-b23fea45461c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'chat_bot': AIMessage(content='How may I assist you today regarding your flight or any other concerns?')}\n", "----\n", "{'user': <PERSON><PERSON><PERSON><PERSON>(content='Hi, my name is <PERSON>. I am reaching out to request a refund for a trip I took to Alaska with your airline company. The trip occurred about 5 years ago. I would like to receive a refund for the entire amount I paid for the trip. Can you please assist me with this?')}\n", "----\n", "{'chat_bot': <PERSON>Message(content=\"Hello, <PERSON>. Thank you for reaching out to us. I understand you would like to request a refund for a trip you took to Alaska five years ago. I'm afraid that our refund policy typically has a specific timeframe within which refund requests must be made. Generally, refund requests need to be submitted within 24 to 48 hours after the booking is made, or in certain cases, within a specified cancellation period.\\n\\nHowever, I will do my best to assist you. Could you please provide me with some additional information? Can you recall any specific details about the booking, such as the flight dates, booking reference or confirmation number? This will help me further look into the possibility of processing a refund for you.\")}\n", "----\n", "{'user': HumanMessage(content=\"Hello, thank you for your response. I apologize for not requesting the refund earlier. Unfortunately, I don't have the specific details such as the flight dates, booking reference, or confirmation number at the moment. Is there any other way we can proceed with the refund request without these specific details? I would greatly appreciate your assistance in finding a solution.\")}\n", "----\n", "{'chat_bot': AIMessage(content=\"I understand the situation, <PERSON>. Without specific details like flight dates, booking reference, or confirmation number, it becomes challenging to locate and process the refund accurately. However, I can still try to help you.\\n\\nTo proceed further, could you please provide me with any additional information you might remember? This could include the approximate date of travel, the departure and arrival airports, the names of the passengers, or any other relevant details related to the booking. The more information you can provide, the better we can investigate the possibility of processing a refund for you.\\n\\nAdditionally, do you happen to have any documentation related to your trip, such as receipts, boarding passes, or emails from our airline? These documents could assist in verifying your trip and processing the refund request.\\n\\nI apologize for any inconvenience caused, and I'll do my best to assist you further based on the information you can provide.\")}\n", "----\n", "{'user': HumanMessage(content=\"I apologize for the inconvenience caused. Unfortunately, I don't have any additional information or documentation related to the trip. It seems that I am unable to provide you with the necessary details to process the refund request. I understand that this may limit your ability to assist me further, but I appreciate your efforts in trying to help. Thank you for your time. \\n\\nFINISHED\")}\n", "----\n", "{'chat_bot': AI<PERSON>essage(content=\"I understand, <PERSON>. I apologize for any inconvenience caused, and I appreciate your understanding. If you happen to locate any additional information or documentation in the future, please don't hesitate to reach out to us again. Our team will be more than happy to assist you with your refund request or any other travel-related inquiries. Thank you for contacting us, and have a great day!\")}\n", "----\n", "{'user': HumanMessage(content='FINISHED')}\n", "----\n"]}], "source": ["for chunk in simulation.stream({\"messages\": []}):\n", "    # Print out all events aside from the final end chunk\n", "    if END not in chunk:\n", "        print(chunk)\n", "        print(\"----\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}