[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-checkpoint-postgres"
version = "2.0.23"
description = "Library with a Postgres implementation of LangGraph checkpoint saver."
authors = []
requires-python = ">=3.9"
readme = "README.md"
license = "MIT"
license-files = ['LICENSE']
dependencies = [
  "langgraph-checkpoint>=2.0.21,<3.0.0",
  "orjson>=3.10.1",
  "psycopg>=3.2.0",
  "psycopg-pool>=3.2.0",
]

[project.urls]
Repository = "https://www.github.com/langchain-ai/langgraph"

[dependency-groups]
dev = [
  "ruff",
  "codespell",
  "pytest",
  "anyio",
  "pytest-asyncio",
  "pytest-mock",
  "mypy",
  "psycopg[binary]",
  "langgraph-checkpoint",
  "pytest-watcher",
]

[tool.uv]
default-groups = ['dev']

[tool.uv.sources]
langgraph-checkpoint = { path = "../checkpoint", editable = true }

[tool.hatch.build.targets.wheel]
include = ["langgraph"]

[tool.pytest.ini_options]
addopts = "--strict-markers --strict-config --durations=5 -vv"
asyncio_mode = "auto"

[tool.ruff]
lint.select = [
  "E",  # pycodestyle
  "F",  # Pyflakes
  "UP", # pyupgrade
  "B",  # flake8-bugbear
  "I",  # isort
]
lint.ignore = ["E501", "B008"]

[tool.mypy]
# https://mypy.readthedocs.io/en/stable/config_file.html
disallow_untyped_defs = "True"
explicit_package_bases = "True"
warn_no_return = "False"
warn_unused_ignores = "True"
warn_redundant_casts = "True"
allow_redefinition = "True"
disable_error_code = "typeddict-item, return-value"

[tool.pytest-watcher]
now = true
delay = 0.1
runner_args = ["--ff", "-x", "-v", "--tb", "short"]
patterns = ["*.py"]
