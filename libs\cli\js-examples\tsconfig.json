{"extends": "@tsconfig/recommended", "compilerOptions": {"target": "ES2021", "lib": ["ES2021", "ES2022.Object", "DOM"], "module": "NodeNext", "moduleResolution": "nodenext", "esModuleInterop": true, "noImplicitReturns": true, "declaration": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "useDefineForClassFields": true, "strictPropertyInitialization": false, "allowJs": true, "strict": true, "strictFunctionTypes": false, "outDir": "dist", "types": ["jest", "node"], "resolveJsonModule": true}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}