name: test

on:
  workflow_call:
    inputs:
      working-directory:
        required: true
        type: string
        description: "From which folder this pipeline executes"

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
          - "3.13"

    name: "test #${{ matrix.python-version }}"
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: astral-sh/setup-uv@v6
        with:
          python-version: ${{ matrix.python-version }}
          enable-cache: true
          cache-suffix: test-${{ inputs.working-directory }}
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        if: ${{ !github.event.pull_request.head.repo.fork }}
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_RO_TOKEN }}

      - name: Install dependencies
        shell: bash
        working-directory: ${{ inputs.working-directory }}
        run: uv sync --frozen --group dev

      - name: Run tests
        shell: bash
        working-directory: ${{ inputs.working-directory }}
        run: make test

      - name: Ensure the tests did not create any additional files
        shell: bash
        working-directory: ${{ inputs.working-directory }}
        run: |
          set -eu

          STATUS="$(git status)"
          echo "$STATUS"

          # grep will exit non-zero if the target message isn't found,
          # and `set -e` above will cause the step to fail.
          echo "$STATUS" | grep 'nothing to commit, working tree clean'
