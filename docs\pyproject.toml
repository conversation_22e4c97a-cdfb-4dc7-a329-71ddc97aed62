[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-docs"
version = "0.0.1"
description = "LangGraph docs"
authors = []
requires-python = "~=3.11"
readme = "README.md"
license = "MIT"
dependencies = [
    "aiohappyeyeballs==2.4.3",
    "hub>=3.0.1,<4",
    "xxhash>=3.5.0,<4",
    "black>=25.1.0,<26",
]

[dependency-groups]
docs = [
    "langgraph",
    "langgraph-prebuilt",
    "langgraph-checkpoint",
    "langgraph-checkpoint-sqlite",
    "langgraph-checkpoint-postgres",
    "langgraph-sdk",
    "langgraph-supervisor",
    "langgraph-swarm",
    "langchain-mcp-adapters",
    "langchain-ollama",
    "mkdocs",
    "mkdocs-autorefs",
    "mkdocstrings",
    "mkdocstrings-python",
    "mkdocs-minify-plugin",
    "mkdocs-rss-plugin",
    "mkdocs-git-committers-plugin-2",
    "mkdocs-material[imaging]",
    "markdown-callouts",
    "markdown-include",
    "mkdocs-exclude",
    "psycopg[binary]",
    "psycopg-pool",
    "pygments-ansi-color",
    "vcrpy",
    "click",
    "ruff",
    "jupyter",
    "langchain-cohere",
    "mkdocs-include-markdown-plugin>=7.1.6",
]
test = [
    "langchain",
    "langchain-core",
    "langchain-openai",
    "langchain-anthropic",
    "langchain-nomic",
    "langchain-fireworks",
    "langchain-community",
    "langchain-tavily",
    "langchain-experimental",
    "langchain-mistralai",
    "langgraph-checkpoint-mongodb",
    "langmem",
    "langsmith",
    "chromadb",
    "gpt4all",
    "scikit-learn",
    "numexpr",
    "numpy",
    "matplotlib",
    "redis",
    "pymongo",
    "motor",
    "grandalf",
    "pyppeteer",
    "networkx",
    "autogen ; python_version >= '3.8' and python_version < '3.13'",
    "pytest",
    "pytest-check-links",
]

[tool.uv]
package = false
default-groups = ["docs","test",]

[tool.uv.sources]
langgraph = { path = "../libs/langgraph/", editable = true }
langgraph-prebuilt = { path = "../libs/prebuilt", editable = true }
langgraph-checkpoint = { path = "../libs/checkpoint/", editable = true }
langgraph-checkpoint-sqlite = { path = "../libs/checkpoint-sqlite", editable = true }
langgraph-checkpoint-postgres = { path = "../libs/checkpoint-postgres", editable = true }
langgraph-sdk = { path = "../libs/sdk-py", editable = true }
langgraph-supervisor = { git = "https://github.com/langchain-ai/langgraph-supervisor-py" }
langgraph-swarm = { git = "https://github.com/langchain-ai/langgraph-swarm-py" }
langchain-mcp-adapters = { git = "https://github.com/langchain-ai/langchain-mcp-adapters" }

[tool.ruff]
extend-include = ["*.ipynb"]

[tool.ruff.lint.per-file-ignores]
"docs/*" = [
    "E402", # allow imports to appear anywhere in docs
    "F401", # allow "imported but unused" example code
    "F811", # allow re-importing the same module, so that cells can stay independent
    "F841", # allow assignments to variables that are never read -- it's example code
    # The issues below should be cleaned up when there's time
    "E722", # allow base imports in notebooks
]

[tool.codespell]
# https://mypy.readthedocs.io/en/stable/config_file.html
# comma-separated list
ignore-words-list = "infor,thead,stdio,nd,jupyter,lets,lite,uis,deque"
# Exclude generated files and directories
skip = "*.ambr,*.lock,*.ipynb,*.yaml,*.zlib,*.css.map,*.js.map"
