#A list of third-party packages to surface on the third-party page.
packages:
  - name: "trustcall"
    repo: "hinthornw/trustcall"
    description: "Tenacious tool calling built on LangGraph."
  - name: "breeze-agent"
    repo: "andrestorres123/breeze-agent"
    description: "A streamlined research system built inspired on STORM and built on LangGraph."
  - name: "langgraph-supervisor"
    repo: "langchain-ai/langgraph-supervisor-py"
    description: "Build supervisor multi-agent systems with LangGraph."
  - name: "langmem"
    repo: "langchain-ai/langmem"
    description: "Build agents that learn and adapt from interactions over time."
  - name: "langchain-mcp-adapters"
    repo: "langchain-ai/langchain-mcp-adapters"
    description: "Make Anthropic Model Context Protocol (MCP) tools compatible with LangGraph agents."
  - name: "open-deep-research"
    repo: "langchain-ai/open_deep_research"
    description: "Open source assistant for iterative web research and report writing."
  - name: "langgraph-swarm"
    repo: "langchain-ai/langgraph-swarm-py"
    description: "Build swarm-style multi-agent systems using LangGraph."
  - name: "delve-taxonomy-generator"
    repo: "andrestorres123/delve"
    description: "A taxonomy generator for unstructured data"
  - name: "nodeology"
    repo: "xyin-anl/Nodeology"
    description: "Enable researcher to build scientific workflows easily with simplified interface."
  - name: "langgraph-bigtool"
    repo: "langchain-ai/langgraph-bigtool"
    description: "Build LangGraph agents with large numbers of tools."
  - name: "ai-data-science-team"
    repo: "business-science/ai-data-science-team"
    description: "An AI-powered data science team of agents to help you perform common data science tasks 10X faster."
  - name: "langgraph-reflection"
    repo: "langchain-ai/langgraph-reflection"
    description: "LangGraph agent that runs a reflection step."
  - name: "langgraph-codeact"
    repo: "langchain-ai/langgraph-codeact"
    description: "LangGraph implementation of CodeAct agent that generates and executes code instead of tool calling."
