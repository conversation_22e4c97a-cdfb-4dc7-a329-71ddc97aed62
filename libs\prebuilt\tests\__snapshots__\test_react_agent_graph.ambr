# serializer version: 1
# name: test_react_agent_graph_structure[None-None-None-tools0]
  '''
  graph TD;
  	__start__ --> agent;
  	agent --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-None-None-tools1]
  '''
  graph TD;
  	__start__ --> agent;
  	agent -.-> __end__;
  	agent -.-> tools;
  	tools --> agent;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-None-pre_model_hook-tools0]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	pre_model_hook --> agent;
  	agent --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-None-pre_model_hook-tools1]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent -.-> __end__;
  	agent -.-> tools;
  	pre_model_hook --> agent;
  	tools --> pre_model_hook;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-post_model_hook-None-tools0]
  '''
  graph TD;
  	__start__ --> agent;
  	agent --> post_model_hook;
  	post_model_hook --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-post_model_hook-None-tools1]
  '''
  graph TD;
  	__start__ --> agent;
  	agent --> post_model_hook;
  	post_model_hook -.-> __end__;
  	post_model_hook -.-> agent;
  	post_model_hook -.-> tools;
  	tools --> agent;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-post_model_hook-pre_model_hook-tools0]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent --> post_model_hook;
  	pre_model_hook --> agent;
  	post_model_hook --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[None-post_model_hook-pre_model_hook-tools1]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent --> post_model_hook;
  	post_model_hook -.-> __end__;
  	post_model_hook -.-> pre_model_hook;
  	post_model_hook -.-> tools;
  	pre_model_hook --> agent;
  	tools --> pre_model_hook;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-None-None-tools0]
  '''
  graph TD;
  	__start__ --> agent;
  	agent --> generate_structured_response;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-None-None-tools1]
  '''
  graph TD;
  	__start__ --> agent;
  	agent -.-> generate_structured_response;
  	agent -.-> tools;
  	tools --> agent;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-None-pre_model_hook-tools0]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent --> generate_structured_response;
  	pre_model_hook --> agent;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-None-pre_model_hook-tools1]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent -.-> generate_structured_response;
  	agent -.-> tools;
  	pre_model_hook --> agent;
  	tools --> pre_model_hook;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-post_model_hook-None-tools0]
  '''
  graph TD;
  	__start__ --> agent;
  	agent --> post_model_hook;
  	post_model_hook --> generate_structured_response;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-post_model_hook-None-tools1]
  '''
  graph TD;
  	__start__ --> agent;
  	agent --> post_model_hook;
  	post_model_hook -.-> agent;
  	post_model_hook -.-> generate_structured_response;
  	post_model_hook -.-> tools;
  	tools --> agent;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-post_model_hook-pre_model_hook-tools0]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent --> post_model_hook;
  	post_model_hook --> generate_structured_response;
  	pre_model_hook --> agent;
  	generate_structured_response --> __end__;
  
  '''
# ---
# name: test_react_agent_graph_structure[ResponseFormat-post_model_hook-pre_model_hook-tools1]
  '''
  graph TD;
  	__start__ --> pre_model_hook;
  	agent --> post_model_hook;
  	post_model_hook -.-> generate_structured_response;
  	post_model_hook -.-> pre_model_hook;
  	post_model_hook -.-> tools;
  	pre_model_hook --> agent;
  	tools --> pre_model_hook;
  	generate_structured_response --> __end__;
  
  '''
# ---
