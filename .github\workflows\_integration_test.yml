name: CLI integration test

on:
  workflow_call:

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version:
          - "3.10"
          - "3.11"
    name: "CLI integration test"
    defaults:
      run:
        working-directory: libs/cli
    steps:
      - uses: actions/checkout@v4
      - name: Get changed files
        id: changed-files
        uses: Ana06/get-changed-files@v2.3.0
        with:
          filter: "libs/cli/**"
      - name: Set up Python ${{ matrix.python-version }}
        if: steps.changed-files.outputs.all
        uses: astral-sh/setup-uv@v6
        with:
          python-version: ${{ matrix.python-version }}
          enable-cache: true
          cache-suffix: "cli-integration-test"
          ignore-nothing-to-cache: true
      - name: Setup env
        if: steps.changed-files.outputs.all
        working-directory: libs/cli/examples
        run: cat .env.example > .env
      - name: Install cli globally
        if: steps.changed-files.outputs.all
        run: pip install -e .
      - name: Build and test service A
        if: steps.changed-files.outputs.all
        working-directory: libs/cli/examples
        run: |
          # The build-arg isn't used; just testing that we accept other args
          langgraph build -t langgraph-test-a  --base-image "langchain/langgraph-trial"
          cp .env.example .envg
          timeout 60 python ../../../.github/scripts/run_langgraph_cli_test.py -c langgraph.json -t langgraph-test-a
      - name: Build and test service B
        if: steps.changed-files.outputs.all
        working-directory: libs/cli/examples/graphs
        run: |
          langgraph build -t langgraph-test-b  --base-image "langchain/langgraph-trial"
          timeout 60 python ../../../../.github/scripts/run_langgraph_cli_test.py -t langgraph-test-b
      - name: Build and test service C
        if: steps.changed-files.outputs.all
        working-directory: libs/cli/examples/graphs_reqs_a
        run: |
          langgraph build -t langgraph-test-c  --base-image "langchain/langgraph-trial"
          timeout 60 python ../../../../.github/scripts/run_langgraph_cli_test.py -t langgraph-test-c
      - name: Build and test service D
        if: steps.changed-files.outputs.all
        working-directory: libs/cli/examples/graphs_reqs_b
        run: |
          langgraph build -t langgraph-test-d  --base-image "langchain/langgraph-trial"
          timeout 60 python ../../../../.github/scripts/run_langgraph_cli_test.py -t langgraph-test-d

      - name: Build JS service
        if: steps.changed-files.outputs.all
        working-directory: libs/cli/js-examples
        run: |
          langgraph build -t langgraph-test-e
