[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "langgraph-checkpoint"
version = "2.1.1"
description = "Library with base interfaces for LangGraph checkpoint savers."
authors = []
requires-python = ">=3.9"
readme = "README.md"
license = "MIT"
license-files = ['LICENSE']
dependencies = [
    "langchain-core>=0.2.38",
    "ormsgpack>=1.10.0",
]

[project.urls]
Repository = "https://www.github.com/langchain-ai/langgraph"

[dependency-groups]
dev = [
  "ruff",
  "codespell",
  "pytest",
  "pytest-asyncio",
  "pytest-mock",
  "pytest-watcher",
  "mypy",
  "dataclasses-json",
  "numpy",
  "pandas",
  "pandas-stubs>=2.2.2.240807",
]

[tool.hatch.build.targets.wheel]
include = ["langgraph"]

[tool.pytest.ini_options]
addopts = "--strict-markers --strict-config --durations=5 -vv"
asyncio_mode = "auto"

[tool.ruff]
lint.select = [
  "E",  # pycodestyle
  "F",  # Pyflakes
  "UP", # pyupgrade
  "B",  # flake8-bugbear
  "I",  # isort
]
lint.ignore = ["E501", "B008"]

[tool.pytest-watcher]
now = true
delay = 0.1
runner_args = ["--ff", "-v", "--tb", "short"]
patterns = ["*.py"]

[tool.mypy]
# https://mypy.readthedocs.io/en/stable/config_file.html
disallow_untyped_defs = "True"
explicit_package_bases = "True"
warn_no_return = "False"
warn_unused_ignores = "True"
warn_redundant_casts = "True"
allow_redefinition = "True"
disable_error_code = "typeddict-item, return-value"
