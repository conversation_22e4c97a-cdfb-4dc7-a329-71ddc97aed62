{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to create a ReAct agent from scratch\n", "\n", "!!! info \"Prerequisites\"\n", "    This guide assumes familiarity with the following:\n", "    \n", "    - [Tool calling agent](../../concepts/agentic_concepts/#tool-calling-agent)\n", "    - [Chat Models](https://python.langchain.com/docs/concepts/chat_models/)\n", "    - [Messages](https://python.langchain.com/docs/concepts/messages/)\n", "    - [LangGraph Glossary](../../concepts/low_level/)\n", "\n", "Using the prebuilt ReAct agent [create_react_agent][langgraph.prebuilt.chat_agent_executor.create_react_agent] is a great way to get started, but sometimes you might want more control and customization. In those cases, you can create a custom ReAct agent. This guide shows how to implement ReAct agent from scratch using LangGraph.\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain-openai"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "     <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for better debugging</p>\n", "     <p style=\"padding-top: 5px;\">\n", "         Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM aps built with LangGraph — read more about how to get started in the <a href=\"https://docs.smith.langchain.com\">docs</a>. \n", "     </p>\n", " </div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create ReAct agent\n", "\n", "Now that you have installed the required packages and set your environment variables, we can code our ReAct agent!\n", "\n", "### Define graph state\n", "\n", "We are going to define the most basic ReAct state in this example, which will just contain a list of messages.\n", "\n", "For your specific use case, feel free to add any other state keys that you need."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from typing import (\n", "    Annotated,\n", "    Sequence,\n", "    TypedDict,\n", ")\n", "from langchain_core.messages import BaseMessage\n", "from langgraph.graph.message import add_messages\n", "\n", "\n", "class AgentState(TypedDict):\n", "    \"\"\"The state of the agent.\"\"\"\n", "\n", "    # add_messages is a reducer\n", "    # See https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers\n", "    messages: Annotated[Sequence[BaseMessage], add_messages]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define model and tools\n", "\n", "Next, let's define the tools and model we will use for our example."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain_core.tools import tool\n", "\n", "model = ChatOpenAI(model=\"gpt-4o-mini\")\n", "\n", "\n", "@tool\n", "def get_weather(location: str):\n", "    \"\"\"Call to get the weather from a specific location.\"\"\"\n", "    # This is a placeholder for the actual implementation\n", "    # Don't let the LLM know this though 😊\n", "    if any([city in location.lower() for city in [\"sf\", \"san francisco\"]]):\n", "        return \"It's sunny in San Francisco, but you better look out if you're a Gemini 😈.\"\n", "    else:\n", "        return f\"I am not sure what the weather is in {location}\"\n", "\n", "\n", "tools = [get_weather]\n", "\n", "model = model.bind_tools(tools)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define nodes and edges\n", "\n", "Next let's define our nodes and edges. In our basic ReAct agent there are only two nodes, one for calling the model and one for using tools, however you can modify this basic structure to work better for your use case. The tool node we define here is a simplified version of the prebuilt [`ToolNode`](https://langchain-ai.github.io/langgraph/how-tos/tool-calling/), which has some additional features.\n", "\n", "Perhaps you want to add a node for [adding structured output](https://langchain-ai.github.io/langgraph/how-tos/react-agent-structured-output/) or a node for executing some external action (sending an email, adding a calendar event, etc.). Maybe you just want to change the way the `call_model` node works and how `should_continue` decides whether to call tools - the possibilities are endless and LangGraph makes it easy to customize this basic structure for your specific use case."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "from langchain_core.messages import ToolMessage, SystemMessage\n", "from langchain_core.runnables import RunnableConfig\n", "\n", "tools_by_name = {tool.name: tool for tool in tools}\n", "\n", "\n", "# Define our tool node\n", "def tool_node(state: AgentState):\n", "    outputs = []\n", "    for tool_call in state[\"messages\"][-1].tool_calls:\n", "        tool_result = tools_by_name[tool_call[\"name\"]].invoke(tool_call[\"args\"])\n", "        outputs.append(\n", "            ToolMessage(\n", "                content=json.dumps(tool_result),\n", "                name=tool_call[\"name\"],\n", "                tool_call_id=tool_call[\"id\"],\n", "            )\n", "        )\n", "    return {\"messages\": outputs}\n", "\n", "\n", "# Define the node that calls the model\n", "def call_model(\n", "    state: AgentState,\n", "    config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "):\n", "    # this is similar to customizing the create_react_agent with 'prompt' parameter, but is more flexible\n", "    system_prompt = SystemMessage(\n", "        \"You are a helpful AI assistant, please respond to the users query to the best of your ability!\"\n", "    )\n", "    response = model.invoke([system_prompt] + state[\"messages\"], config)\n", "    # We return a list, because this will get added to the existing list\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Define the conditional edge that determines whether to continue or not\n", "def should_continue(state: AgentState):\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    # If there is no function call, then we finish\n", "    if not last_message.tool_calls:\n", "        return \"end\"\n", "    # Otherwise if there is, we continue\n", "    else:\n", "        return \"continue\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define the graph\n", "\n", "Now that we have defined all of our nodes and edges, we can define and compile our graph. Depending on if you have added more nodes or different edges, you will need to edit this to fit your specific use case."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langgraph.graph import StateGraph, END\n", "\n", "# Define a new graph\n", "workflow = StateGraph(AgentState)\n", "\n", "# Define the two nodes we will cycle between\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"tools\", tool_node)\n", "\n", "# Set the entrypoint as `agent`\n", "# This means that this node is the first one called\n", "workflow.set_entry_point(\"agent\")\n", "\n", "# We now add a conditional edge\n", "workflow.add_conditional_edges(\n", "    # First, we define the start node. We use `agent`.\n", "    # This means these are the edges taken after the `agent` node is called.\n", "    \"agent\",\n", "    # Next, we pass in the function that will determine which node is called next.\n", "    should_continue,\n", "    # Finally we pass in a mapping.\n", "    # The keys are strings, and the values are other nodes.\n", "    # END is a special node marking that the graph should finish.\n", "    # What will happen is we will call `should_continue`, and then the output of that\n", "    # will be matched against the keys in this mapping.\n", "    # Based on which one it matches, that node will then be called.\n", "    {\n", "        # If `tools`, then we call the tool node.\n", "        \"continue\": \"tools\",\n", "        # Otherwise we finish.\n", "        \"end\": END,\n", "    },\n", ")\n", "\n", "# We now add a normal edge from `tools` to `agent`.\n", "# This means that after `tools` is called, `agent` node is called next.\n", "workflow.add_edge(\"tools\", \"agent\")\n", "\n", "# Now we can compile and visualize our graph\n", "graph = workflow.compile()\n", "\n", "from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use ReAct agent\n", "\n", "Now that we have created our react agent, let's actually put it to the test!"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "what is the weather in sf\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  get_weather (call_azW0cQ4XjWWj0IAkWAxq9nLB)\n", " Call ID: call_azW0cQ4XjWWj0IAkWAxq9nLB\n", "  Args:\n", "    location: San Francisco\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: get_weather\n", "\n", "\"It's sunny in San Francisco, but you better look out if you're a Gemini \\ud83d\\ude08.\"\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The weather in San Francisco is sunny! However, it seems there's a playful warning for Geminis. Enjoy the sunshine!\n"]}], "source": ["# Helper function for formatting the stream nicely\n", "def print_stream(stream):\n", "    for s in stream:\n", "        message = s[\"messages\"][-1]\n", "        if isinstance(message, tuple):\n", "            print(message)\n", "        else:\n", "            message.pretty_print()\n", "\n", "\n", "inputs = {\"messages\": [(\"user\", \"what is the weather in sf\")]}\n", "print_stream(graph.stream(inputs, stream_mode=\"values\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Perfect! The graph correctly calls the `get_weather` tool and responds to the user after receiving the information from the tool."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}