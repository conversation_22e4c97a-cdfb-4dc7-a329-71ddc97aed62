name: notebook-tests
services:
  mongo:
    image: mongo:latest
    ports:
      - "27017:27017"
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
  postgres:
    image: postgres:16
    ports:
      - "5442:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    healthcheck:
      test: pg_isready -U postgres
      start_period: 10s
      timeout: 1s
      retries: 5
      interval: 60s
      start_interval: 1s
