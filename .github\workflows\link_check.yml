name: Check Docs & Links

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main
  schedule:
    - cron: "0 5 * * *"
  workflow_dispatch:

permissions:
  contents: read

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check links in Markdown files
        uses: gaurav-nelson/github-action-markdown-link-check@v1
        with:
          folder-path: "docs/"
          check-modified-files-only: ${{ github.event_name != 'schedule' }}
          file-path: "./README.md"
          config-file: "./.markdown-link-check.config.json"

  check-readmes-synced:
    # This checks that the repo README.md is identical to the libs/langgraph/README.md
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Check README.md is in sync
        run: |
          if ! diff -q README.md libs/langgraph/README.md >/dev/null; then
          echo "README.md is out of sync with libs/langgraph/README.md"
          diff -C 3 README.md libs/langgraph/README.md
          exit 1
          fi
